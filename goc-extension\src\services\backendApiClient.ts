/**
 * Backend API Client for VS Code Extension
 * 
 * Handles authentication and API requests to GOC Agent backend
 */

import * as vscode from 'vscode';
import fetch from 'node-fetch';

export interface User {
  id: string;
  name: string;
  email: string;
  subscription?: {
    plan: string;
    status: string;
    api_requests_remaining: number;
    api_requests_limit: number;
    expires_at?: string;
  };
}

export interface AuthResponse {
  success: boolean;
  token?: string;
  user?: User;
  message?: string;
}

export interface ChatRequest {
  messages: Array<{
    role: string;
    content: string;
  }>;
  model?: string;
  provider?: string;
  session_id?: string;
  max_tokens?: number;
  temperature?: number;
}

export interface ChatResponse {
  content: string;
  model: string;
  provider: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  session_id?: string;
}

export class BackendAPIClient {
  private baseUrl: string;
  private token: string | null = null;
  private context: vscode.ExtensionContext;

  constructor(context: vscode.ExtensionContext) {
    this.context = context;
    this.baseUrl = 'https://api.goc-agent.com'; // Production URL
    // this.baseUrl = 'http://localhost:8000'; // Development URL
    
    // Load saved token
    this.token = this.context.globalState.get('goc_auth_token', null);
  }

  private async makeRequest(endpoint: string, options: any = {}): Promise<any> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers: any = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'GOC-Agent-Extension/1.0.0',
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    const response = await fetch(url, {
      ...options,
      headers: {
        ...headers,
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  }

  public isAuthenticated(): boolean {
    return !!this.token;
  }

  public async register(name: string, email: string, password: string): Promise<AuthResponse> {
    try {
      const response = await this.makeRequest('/auth/register', {
        method: 'POST',
        body: JSON.stringify({
          name,
          email,
          password,
          password_confirmation: password,
        }),
      });

      if (response.success && response.token) {
        await this.saveToken(response.token);
      }

      return {
        success: response.success,
        token: response.token,
        user: response.user,
        message: response.message,
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.message || 'Registration failed',
      };
    }
  }

  public async login(email: string, password: string): Promise<AuthResponse> {
    try {
      const response = await this.makeRequest('/auth/login', {
        method: 'POST',
        body: JSON.stringify({
          email,
          password,
        }),
      });

      if (response.success && response.token) {
        await this.saveToken(response.token);
      }

      return {
        success: response.success,
        token: response.token,
        user: response.user,
        message: response.message,
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.message || 'Login failed',
      };
    }
  }

  public async logout(): Promise<void> {
    try {
      if (this.isAuthenticated()) {
        await this.makeRequest('/auth/logout', {
          method: 'POST',
        });
      }
    } catch (error) {
      // Ignore logout errors
    } finally {
      await this.clearToken();
    }
  }

  public async getCurrentUser(): Promise<User> {
    const response = await this.makeRequest('/auth/user');
    return response.user;
  }

  public async chat(request: ChatRequest): Promise<ChatResponse> {
    const response = await this.makeRequest('/agent/chat', {
      method: 'POST',
      body: JSON.stringify(request),
    });
    return response;
  }

  public async executeTask(
    task: string,
    sessionId?: string,
    provider?: string,
    model?: string,
    autoMode?: boolean
  ): Promise<any> {
    const response = await this.makeRequest('/agent/task', {
      method: 'POST',
      body: JSON.stringify({
        task,
        session_id: sessionId,
        provider,
        model,
        auto_mode: autoMode,
      }),
    });
    return response;
  }

  public async getProviders(): Promise<any> {
    const response = await this.makeRequest('/agent/providers');
    return response;
  }

  public async getStatus(): Promise<any> {
    try {
      const response = await this.makeRequest('/agent/status');
      return response;
    } catch (error) {
      return { status: 'error', message: 'Backend not available' };
    }
  }

  public async healthCheck(): Promise<boolean> {
    try {
      const response = await this.makeRequest('/health');
      return response.status === 'ok';
    } catch (error) {
      return false;
    }
  }

  private async saveToken(token: string): Promise<void> {
    this.token = token;
    await this.context.globalState.update('goc_auth_token', token);
  }

  private async clearToken(): Promise<void> {
    this.token = null;
    await this.context.globalState.update('goc_auth_token', null);
  }

  /**
   * Check if user has access to GOC Agent models
   */
  public async validateGocAgentAccess(): Promise<{
    hasAccess: boolean;
    plan: string;
    requestsRemaining: number;
    message?: string;
  }> {
    try {
      if (!this.isAuthenticated()) {
        return {
          hasAccess: false,
          plan: 'none',
          requestsRemaining: 0,
          message: 'Authentication required'
        };
      }

      const user = await this.getCurrentUser();
      const subscription = user.subscription;

      if (!subscription) {
        return {
          hasAccess: false,
          plan: 'none',
          requestsRemaining: 0,
          message: 'No subscription found'
        };
      }

      return {
        hasAccess: subscription.api_requests_remaining > 0,
        plan: subscription.plan,
        requestsRemaining: subscription.api_requests_remaining,
        message: subscription.api_requests_remaining === 0 ? 'Request limit exceeded' : undefined
      };
    } catch (error: any) {
      return {
        hasAccess: false,
        plan: 'error',
        requestsRemaining: 0,
        message: error.message || 'Failed to validate access'
      };
    }
  }
}
