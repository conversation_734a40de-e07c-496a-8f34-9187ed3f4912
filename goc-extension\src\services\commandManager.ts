import * as vscode from 'vscode';
import { GocCoreService } from './gocCoreService';
import { Chat<PERSON>rovider } from '../providers/chatProvider';
import { StatusBarManager } from './statusBarManager';
import { ConfigurationManager } from './configurationManager';

export class CommandManager implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];

    constructor(
        private context: vscode.ExtensionContext,
        private gocCoreService: GocCoreService,
        private chatProvider: ChatProvider,
        private statusBarManager: StatusBarManager,
        private configurationManager: ConfigurationManager
    ) {}

    // Simple context manager methods
    private getContextAroundSelection(document: vscode.TextDocument, selection: vscode.Selection): string {
        const startLine = Math.max(0, selection.start.line - 5);
        const endLine = Math.min(document.lineCount - 1, selection.end.line + 5);
        const range = new vscode.Range(startLine, 0, endLine, document.lineAt(endLine).text.length);
        return document.getText(range);
    }

    private getFileContext(document: vscode.TextDocument): string {
        // Return first 100 lines as context
        const endLine = Math.min(document.lineCount - 1, 100);
        const range = new vscode.Range(0, 0, endLine, document.lineAt(endLine).text.length);
        return document.getText(range);
    }

    registerCommands(): void {
        // Chat commands
        this.registerCommand('gocAgent.openChat', () => this.openChat());

        // Code analysis commands
        this.registerCommand('gocAgent.explainCode', () => this.explainCode());
        this.registerCommand('gocAgent.generateCode', () => this.generateCode());
        this.registerCommand('gocAgent.fixIssues', () => this.fixIssues());
        this.registerCommand('gocAgent.refactorCode', () => this.refactorCode());
        this.registerCommand('gocAgent.generateTests', () => this.generateTests());

        // Project commands
        this.registerCommand('gocAgent.analyzeProject', () => this.analyzeProject());

        // Research commands
        this.registerCommand('gocAgent.webResearch', () => this.webResearch());
        this.registerCommand('gocAgent.trainTechnology', () => this.trainTechnology());

        // Configuration commands
        this.registerCommand('gocAgent.selectProvider', () => this.selectProvider());
        this.registerCommand('gocAgent.selectModel', () => this.selectModel());
        this.registerCommand('gocAgent.refreshOllamaModels', () => this.refreshOllamaModels());
        this.registerCommand('gocAgent.checkOllamaStatus', () => this.checkOllamaStatus());
        this.registerCommand('gocAgent.setupOllama', () => this.setupOllama());

        // Authentication commands
        this.registerCommand('gocAgent.login', () => this.login());
        this.registerCommand('gocAgent.register', () => this.register());
        this.registerCommand('gocAgent.logout', () => this.logout());
        this.registerCommand('gocAgent.showProfile', () => this.showProfile());
        this.registerCommand('gocAgent.checkAuthStatus', () => this.checkAuthStatus());
    }

    private registerCommand(command: string, callback: (...args: any[]) => any): void {
        const disposable = vscode.commands.registerCommand(command, callback);
        this.disposables.push(disposable);
    }

    private async openChat(): Promise<void> {
        try {
            await vscode.commands.executeCommand('workbench.view.extension.gocAgent');
            await vscode.commands.executeCommand('gocAgent.chatView.focus');
        } catch (error) {
            console.error('Error opening chat:', error);
            vscode.window.showErrorMessage('Failed to open GOC Agent chat');
        }
    }

    private async explainCode(): Promise<void> {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor found');
            return;
        }

        try {
            this.statusBarManager.updateStatus('working', 'Explaining code...');

            const selection = editor.selection;
            const document = editor.document;
            
            let codeToExplain: string;
            let context: string;

            if (!selection.isEmpty) {
                // Explain selected code
                codeToExplain = document.getText(selection);
                context = this.getContextAroundSelection(document, selection);
            } else {
                // Explain entire file
                codeToExplain = document.getText();
                context = this.getFileContext(document);
            }

            const explanation = await this.gocCoreService.explainCode(
                document.fileName,
                codeToExplain,
                context
            );

            // Show explanation in chat
            this.chatProvider.addMessage('assistant', explanation);
            await this.openChat();

        } catch (error) {
            this.handleError('Failed to explain code', error);
        } finally {
            this.statusBarManager.updateStatus('ready', 'GOC Agent is ready');
        }
    }

    private async generateCode(): Promise<void> {
        try {
            const prompt = await vscode.window.showInputBox({
                prompt: 'Describe the code you want to generate',
                placeHolder: 'e.g., Create a function that validates email addresses',
                validateInput: (value) => {
                    if (!value || value.trim().length === 0) {
                        return 'Please enter a description';
                    }
                    return null;
                }
            });

            if (!prompt) return;

            this.statusBarManager.updateStatus('working', 'Generating code...');

            const editor = vscode.window.activeTextEditor;
            const context = editor ? this.getFileContext(editor.document) : '';

            const generatedCode = await this.gocCoreService.generateCode(prompt, context);

            // Show generated code in chat
            this.chatProvider.addMessage('user', `Generate: ${prompt}`);
            this.chatProvider.addMessage('assistant', `\`\`\`\n${generatedCode}\n\`\`\``);
            await this.openChat();

        } catch (error) {
            this.handleError('Failed to generate code', error);
        } finally {
            this.statusBarManager.updateStatus('ready', 'GOC Agent is ready');
        }
    }

    private async fixIssues(): Promise<void> {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor found');
            return;
        }

        try {
            this.statusBarManager.updateStatus('working', 'Fixing issues...');

            const code = editor.document.getText();
            const fixes = await this.gocCoreService.fixIssues(editor.document.fileName, code);

            // Show fixes in chat
            this.chatProvider.addMessage('assistant', fixes);
            await this.openChat();

        } catch (error) {
            this.handleError('Failed to fix issues', error);
        } finally {
            this.statusBarManager.updateStatus('ready', 'GOC Agent is ready');
        }
    }

    private async refactorCode(): Promise<void> {
        const editor = vscode.window.activeTextEditor;
        if (!editor || editor.selection.isEmpty) {
            vscode.window.showWarningMessage('Please select code to refactor');
            return;
        }

        try {
            const refactorType = await vscode.window.showQuickPick([
                { label: 'Extract Function', value: 'extract-function' },
                { label: 'Rename Variable', value: 'rename-variable' },
                { label: 'Simplify Logic', value: 'simplify' },
                { label: 'Optimize Performance', value: 'optimize' },
                { label: 'Improve Readability', value: 'readability' }
            ], {
                placeHolder: 'Select refactoring type'
            });

            if (!refactorType) return;

            this.statusBarManager.updateStatus('working', 'Refactoring code...');

            const selectedCode = editor.document.getText(editor.selection);
            const context = this.getContextAroundSelection(editor.document, editor.selection);

            // For now, use generate command with refactoring prompt
            const prompt = `Refactor this code (${refactorType.label}):\n\n${selectedCode}`;
            const refactoredCode = await this.gocCoreService.generateCode(prompt, context);

            // Show refactored code in chat
            this.chatProvider.addMessage('user', `Refactor (${refactorType.label})`);
            this.chatProvider.addMessage('assistant', `\`\`\`\n${refactoredCode}\n\`\`\``);
            await this.openChat();

        } catch (error) {
            this.handleError('Failed to refactor code', error);
        } finally {
            this.statusBarManager.updateStatus('ready', 'GOC Agent is ready');
        }
    }

    private async generateTests(): Promise<void> {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor found');
            return;
        }

        try {
            this.statusBarManager.updateStatus('working', 'Generating tests...');

            const code = editor.selection.isEmpty 
                ? editor.document.getText()
                : editor.document.getText(editor.selection);

            const prompt = `Generate unit tests for this code:\n\n${code}`;
            const context = this.getFileContext(editor.document);

            const tests = await this.gocCoreService.generateCode(prompt, context);

            // Show tests in chat
            this.chatProvider.addMessage('user', 'Generate tests');
            this.chatProvider.addMessage('assistant', `\`\`\`\n${tests}\n\`\`\``);
            await this.openChat();

        } catch (error) {
            this.handleError('Failed to generate tests', error);
        } finally {
            this.statusBarManager.updateStatus('ready', 'GOC Agent is ready');
        }
    }

    private async analyzeProject(): Promise<void> {
        try {
            this.statusBarManager.updateStatus('working', 'Analyzing project...');

            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            if (!workspaceFolder) {
                vscode.window.showWarningMessage('No workspace folder found');
                return;
            }

            const analysis = await this.gocCoreService.analyzeProject(workspaceFolder.uri.fsPath);

            // Show analysis in chat
            this.chatProvider.addMessage('assistant', analysis);
            await this.openChat();

        } catch (error) {
            this.handleError('Failed to analyze project', error);
        } finally {
            this.statusBarManager.updateStatus('ready', 'GOC Agent is ready');
        }
    }

    private async webResearch(): Promise<void> {
        try {
            const query = await vscode.window.showInputBox({
                prompt: 'Enter your research query',
                placeHolder: 'e.g., How to implement OAuth in Node.js',
                validateInput: (value) => {
                    if (!value || value.trim().length === 0) {
                        return 'Please enter a query';
                    }
                    return null;
                }
            });

            if (!query) return;

            this.statusBarManager.updateStatus('working', 'Researching...');

            const results = await this.gocCoreService.webResearch(query);

            // Show research results in chat
            this.chatProvider.addMessage('user', `Research: ${query}`);
            this.chatProvider.addMessage('assistant', results);
            await this.openChat();

        } catch (error) {
            this.handleError('Failed to perform web research', error);
        } finally {
            this.statusBarManager.updateStatus('ready', 'GOC Agent is ready');
        }
    }

    private async trainTechnology(): Promise<void> {
        try {
            const technology = await vscode.window.showQuickPick([
                'JavaScript',
                'TypeScript',
                'Python',
                'PHP',
                'Laravel',
                'React',
                'Vue.js',
                'Node.js',
                'Flutter',
                'Custom...'
            ], {
                placeHolder: 'Select technology to train on'
            });

            if (!technology) return;

            let techName = technology;
            if (technology === 'Custom...') {
                const customTech = await vscode.window.showInputBox({
                    prompt: 'Enter technology name',
                    placeHolder: 'e.g., FastAPI, Django, etc.'
                });
                if (!customTech) return;
                techName = customTech;
            }

            this.statusBarManager.updateStatus('working', `Training on ${techName}...`);

            // Use CLI training command
            await this.gocCoreService.runInteractiveCommand(['train', '-t', techName]);

        } catch (error) {
            this.handleError('Failed to start training', error);
        } finally {
            this.statusBarManager.updateStatus('ready', 'GOC Agent is ready');
        }
    }

    private async selectProvider(): Promise<void> {
        try {
            const selectedProvider = await this.configurationManager.selectProvider();
            if (selectedProvider) {
                this.statusBarManager.updateProviderInfo();
                vscode.window.showInformationMessage(`Switched to ${selectedProvider}`);
            }
        } catch (error) {
            this.handleError('Failed to select provider', error);
        }
    }

    private async selectModel(): Promise<void> {
        try {
            const selectedModel = await this.configurationManager.selectModel();
            if (selectedModel) {
                this.statusBarManager.updateProviderInfo();
                vscode.window.showInformationMessage(`Switched to ${selectedModel}`);
            }
        } catch (error) {
            this.handleError('Failed to select model', error);
        }
    }

    private async refreshOllamaModels(): Promise<void> {
        try {
            this.statusBarManager.updateStatus('working', 'Refreshing Ollama models...');
            await this.configurationManager.refreshOllamaModels();

            const models = await this.configurationManager.getModelsByProvider('ollama');
            if (models.length > 0) {
                vscode.window.showInformationMessage(
                    `Found ${models.length} Ollama models: ${models.slice(0, 3).join(', ')}${models.length > 3 ? '...' : ''}`
                );
            } else {
                vscode.window.showWarningMessage('No Ollama models found. Install models with: ollama pull llama3.2:3b');
            }
        } catch (error) {
            this.handleError('Failed to refresh Ollama models', error);
        } finally {
            this.statusBarManager.updateStatus('ready', 'GOC Agent is ready');
        }
    }

    private async checkOllamaStatus(): Promise<void> {
        try {
            const status = await this.configurationManager.checkOllamaStatus();

            if (!status.isRunning) {
                vscode.window.showErrorMessage('Ollama is not running. Please start Ollama first.');
            } else if (!status.hasModels) {
                vscode.window.showWarningMessage('Ollama is running but no models are installed.');
            } else {
                vscode.window.showInformationMessage(
                    `Ollama Status: ✅ Running with ${status.models.length} models available`
                );
            }
        } catch (error) {
            this.handleError('Failed to check Ollama status', error);
        }
    }

    private async setupOllama(): Promise<void> {
        try {
            await this.configurationManager.showOllamaSetupDialog();
        } catch (error) {
            this.handleError('Failed to show Ollama setup', error);
        }
    }

    private async login(): Promise<void> {
        try {
            await this.configurationManager.showLoginDialog();
            this.statusBarManager.updateProviderInfo();
        } catch (error) {
            this.handleError('Failed to login', error);
        }
    }

    private async register(): Promise<void> {
        try {
            await this.configurationManager.showRegistrationDialog();
            this.statusBarManager.updateProviderInfo();
        } catch (error) {
            this.handleError('Failed to register', error);
        }
    }

    private async logout(): Promise<void> {
        try {
            await this.configurationManager.logout();
            this.statusBarManager.updateProviderInfo();
        } catch (error) {
            this.handleError('Failed to logout', error);
        }
    }

    private async showProfile(): Promise<void> {
        try {
            await this.configurationManager.showUserProfile();
        } catch (error) {
            this.handleError('Failed to show profile', error);
        }
    }

    private async checkAuthStatus(): Promise<void> {
        try {
            const status = this.configurationManager.getDetailedStatus();

            if (status.requiresAuth) {
                if (status.isAuthenticated) {
                    vscode.window.showInformationMessage(
                        `✅ Authenticated with ${status.provider} (${status.model})`,
                        'View Profile'
                    ).then(selection => {
                        if (selection === 'View Profile') {
                            this.showProfile();
                        }
                    });
                } else {
                    vscode.window.showWarningMessage(
                        `❌ Not authenticated with ${status.provider}`,
                        'Login',
                        'Register'
                    ).then(selection => {
                        if (selection === 'Login') {
                            this.login();
                        } else if (selection === 'Register') {
                            this.register();
                        }
                    });
                }
            } else {
                vscode.window.showInformationMessage(
                    `✅ Using ${status.provider} (${status.model}) - No authentication required`
                );
            }
        } catch (error) {
            this.handleError('Failed to check authentication status', error);
        }
    }

    private handleError(message: string, error: any): void {
        console.error(message, error);
        this.statusBarManager.showError(message);
        vscode.window.showErrorMessage(`${message}: ${error.message || error}`);
    }

    getDisposables(): vscode.Disposable[] {
        return [...this.disposables];
    }

    dispose(): void {
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}
