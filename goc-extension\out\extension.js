"use strict";
/**
 * GOC Agent VS Code Extension
 *
 * Clean, minimal extension powered by GOC Core
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = __importStar(require("vscode"));
const gocCoreService_1 = require("./services/gocCoreService");
const chatProvider_1 = require("./providers/chatProvider");
const statusBarManager_1 = require("./services/statusBarManager");
const commandManager_1 = require("./services/commandManager");
const fileWatcherService_1 = require("./services/fileWatcherService");
const researchProvider_1 = require("./providers/researchProvider");
const configurationManager_1 = require("./services/configurationManager");
// Global services - minimal and clean
let gocCoreService;
let chatProvider;
let statusBarManager;
let commandManager;
let fileWatcherService;
let researchProvider;
let configurationManager;
/**
 * Clean Extension Activation - Powered by GOC Core
 */
async function activate(context) {
    try {
        console.log('🚀 Activating GOC Agent extension...');
        // Show progress to user
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Window,
            title: 'Initializing GOC Agent...',
            cancellable: false
        }, async (progress) => {
            // Step 1: Initialize GOC Core Service (all the heavy lifting)
            progress.report({ increment: 30, message: 'Loading AI services...' });
            gocCoreService = new gocCoreService_1.GocCoreService(context);
            await gocCoreService.initialize();
            // Step 2: Initialize minimal UI services
            progress.report({ increment: 30, message: 'Setting up interface...' });
            statusBarManager = new statusBarManager_1.StatusBarManager(gocCoreService);
            chatProvider = new chatProvider_1.ChatProvider(context, gocCoreService);
            configurationManager = new configurationManager_1.ConfigurationManager(context);
            commandManager = new commandManager_1.CommandManager(context, gocCoreService, chatProvider, statusBarManager, configurationManager);
            fileWatcherService = new fileWatcherService_1.FileWatcherService(context, gocCoreService);
            researchProvider = new researchProvider_1.ResearchProvider(context, gocCoreService);
            // Step 3: Register everything
            progress.report({ increment: 30, message: 'Registering services...' });
            await registerServices(context);
            // Step 4: Final setup
            progress.report({ increment: 10, message: 'Finalizing...' });
            await statusBarManager.refreshStatus();
        });
        // Show success notification
        const status = await gocCoreService.getStatus();
        vscode.window.showInformationMessage(`🤖 GOC Agent ready! Using ${status.provider} - ${status.model}`);
        console.log('✅ GOC Agent extension activated successfully');
    }
    catch (error) {
        console.error('❌ Failed to activate GOC Agent:', error);
        vscode.window.showErrorMessage(`GOC Agent activation failed: ${error}`);
    }
}
exports.activate = activate;
/**
 * Register Services - Clean and Minimal
 */
async function registerServices(context) {
    // Register chat webview provider
    const chatViewProvider = vscode.window.registerWebviewViewProvider(chatProvider_1.ChatProvider.viewType, chatProvider, {
        webviewOptions: {
            retainContextWhenHidden: true
        }
    });
    // Register configuration change listener
    const configChangeListener = vscode.workspace.onDidChangeConfiguration(async (event) => {
        if (event.affectsConfiguration('gocAgent')) {
            // Reinitialize goc-core with new settings
            await gocCoreService.initialize();
            await statusBarManager.refreshStatus();
        }
    });
    // Register file save listener for intelligent context updates
    const fileSaveListener = vscode.workspace.onDidSaveTextDocument(async (document) => {
        // Auto-update context when files are saved
        // This uses goc-core's context engine automatically
        console.log(`File saved: ${document.fileName} - Context updated`);
    });
    // Add all disposables to context
    context.subscriptions.push(chatViewProvider, configChangeListener, fileSaveListener, statusBarManager, fileWatcherService, researchProvider);
}
/**
 * Clean Extension Deactivation
 */
async function deactivate() {
    console.log('🔄 Deactivating GOC Agent extension...');
    try {
        // Dispose services in reverse order
        if (researchProvider) {
            researchProvider.dispose();
        }
        if (fileWatcherService) {
            fileWatcherService.dispose();
        }
        if (statusBarManager) {
            statusBarManager.dispose();
        }
        if (gocCoreService) {
            await gocCoreService.dispose();
        }
        console.log('✅ GOC Agent extension deactivated successfully');
    }
    catch (error) {
        console.error('❌ Error during deactivation:', error);
    }
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map