/**
 * Monitoring Command
 *
 * Performance monitoring, usage analytics, and system health tracking
 */
import { Command } from 'commander';
import { GocCore } from '@goc-agent/core';
export declare class MonitorCommand {
    private core;
    constructor(core: GocCore);
    register(program: Command): void;
    private checkHealth;
    private showPerformance;
    private showUsage;
    showMetrics(options: any): Promise<void>;
    private exportMetrics;
    private recordMetric;
    showStatus(): Promise<void>;
    private formatDuration;
}
//# sourceMappingURL=monitor.d.ts.map