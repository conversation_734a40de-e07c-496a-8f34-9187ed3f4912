#!/usr/bin/env node
"use strict";
/**
 * GOC Agent CLI
 *
 * Command-line interface powered by GOC Core
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const commander_1 = require("commander");
const chalk_1 = __importDefault(require("chalk"));
const boxen_1 = __importDefault(require("boxen"));
const ora_1 = __importDefault(require("ora"));
const core_1 = require("@goc-agent/core");
const chat_1 = require("./commands/chat");
const config_1 = require("./commands/config");
const analyze_1 = require("./commands/analyze");
const train_1 = require("./commands/train");
const auth_1 = require("./commands/auth");
const session_1 = require("./commands/session");
const tools_1 = require("./commands/tools");
const context_1 = require("./commands/context");
const web_1 = require("./commands/web");
const auto_1 = require("./commands/auto");
const generate_1 = require("./commands/generate");
const monitor_1 = require("./commands/monitor");
const docs_1 = require("./commands/docs");
const deploy_1 = require("./commands/deploy");
const models_1 = require("./commands/models");
const BackendAPIClient_1 = require("../services/BackendAPIClient");
class GocCLI {
    constructor() {
        this.core = null;
        this.configManager = null;
        this.program = new commander_1.Command();
        this.apiClient = new BackendAPIClient_1.BackendAPIClient();
        this.setupProgram();
    }
    setupProgram() {
        this.program
            .name('goc')
            .description('GOC Agent - AI-powered coding assistant')
            .version('1.0.0')
            .option('-v, --verbose', 'Enable verbose output')
            .option('-q, --quiet', 'Suppress non-essential output')
            .hook('preAction', async (thisCommand) => {
            await this.initialize(thisCommand.opts());
        });
        // Register all commands (structure first, then connect to core after init)
        this.registerAllCommands();
    }
    registerAllCommands() {
        // Register commands that don't need core initialization
        // Register authentication command
        const authCommand = new auth_1.AuthCommand(this.apiClient);
        authCommand.registerCommands(this.program);
        // Register session management command
        const sessionCommand = new session_1.SessionCommand(this.apiClient);
        sessionCommand.register(this.program);
        // Register tools command
        const toolsCommand = new tools_1.ToolsCommand();
        toolsCommand.register(this.program);
        // Register config command
        const configCommand = new config_1.ConfigCommand();
        configCommand.register(this.program);
        // Register models command
        const modelsCommand = new models_1.ModelsCommand();
        modelsCommand.register(this.program);
        // Status command
        this.program
            .command('status')
            .description('Show GOC Agent status')
            .action(async () => {
            await this.showStatus();
        });
        // Register intelligent commands with lazy initialization
        this.registerIntelligentCommands();
    }
    registerIntelligentCommands() {
        // Register context command with lazy core access
        this.program
            .command('context')
            .description('Intelligent context analysis and search')
            .addCommand(this.program.createCommand('analyze')
            .argument('<query>', 'Query to analyze')
            .option('-p, --project <path>', 'Project path')
            .action(async (query, options) => {
            await this.ensureInitialized();
            const contextCommand = new context_1.ContextCommand(this.core);
            await contextCommand.analyzeContext(query, options);
        }))
            .addCommand(this.program.createCommand('search')
            .argument('<query>', 'Search query')
            .option('-l, --limit <number>', 'Max results', '10')
            .action(async (query, options) => {
            await this.ensureInitialized();
            const contextCommand = new context_1.ContextCommand(this.core);
            await contextCommand.searchContext(query, options);
        }));
        // Register web command with lazy core access
        this.program
            .command('web')
            .description('Intelligent web research and content processing')
            .addCommand(this.program.createCommand('research')
            .argument('<topic>', 'Topic to research')
            .option('-d, --depth <level>', 'Research depth (basic|intermediate|advanced)', 'intermediate')
            .option('-s, --sources <number>', 'Maximum sources', '10')
            .option('--examples', 'Include code examples')
            .option('--trends', 'Include trend analysis')
            .action(async (topic, options) => {
            await this.ensureInitialized();
            const webCommand = new web_1.WebCommand(this.core);
            await webCommand.researchTopic(topic, options);
        }))
            .addCommand(this.program.createCommand('search')
            .argument('<query>', 'Search query')
            .option('-m, --max <number>', 'Maximum results', '10')
            .option('-l, --language <lang>', 'Search language', 'en')
            .action(async (query, options) => {
            await this.ensureInitialized();
            const webCommand = new web_1.WebCommand(this.core);
            await webCommand.searchWeb(query, options);
        }));
        // Register auto mode command
        this.program
            .command('auto')
            .description('Autonomous intelligent task execution')
            .addCommand(this.program.createCommand('execute')
            .argument('<goal>', 'Goal to execute')
            .option('-t, --timeout <minutes>', 'Timeout in minutes', '30')
            .option('--safe', 'Enable safe mode')
            .action(async (goal, options) => {
            await this.ensureInitialized();
            const autoCommand = new auto_1.AutoCommand(this.core);
            await autoCommand.executeGoal(goal, options);
        }));
        // Register chat command
        this.program
            .command('chat')
            .description('Interactive AI chat with learning')
            .option('-m, --model <model>', 'AI model to use')
            .option('-p, --provider <provider>', 'AI provider to use')
            .action(async (options) => {
            await this.ensureInitialized();
            const chatCommand = new chat_1.ChatCommand(this.core, this.configManager, this.apiClient);
            await chatCommand.startInteractiveChat(options);
        });
        // Register analyze command
        this.program
            .command('analyze')
            .description('Analyze code and projects')
            .argument('[path]', 'Path to analyze', '.')
            .option('--metrics', 'Include code metrics')
            .option('--suggestions', 'Include improvement suggestions')
            .action(async (path, options) => {
            await this.ensureInitialized();
            const analyzeCommand = new analyze_1.AnalyzeCommand(this.core);
            await analyzeCommand.analyzeProject(path, options);
        });
        // Register generate command
        this.program
            .command('generate')
            .description('Generate code using AI')
            .addCommand(this.program.createCommand('code')
            .argument('<description>', 'Code description')
            .option('-l, --language <lang>', 'Programming language')
            .option('-f, --file <path>', 'Output file path')
            .action(async (description, options) => {
            await this.ensureInitialized();
            const generateCommand = new generate_1.GenerateCommand(this.core);
            await generateCommand.generateCode(description, options);
        }))
            .addCommand(this.program.createCommand('tests')
            .argument('<filePath>', 'File to generate tests for')
            .option('-f, --framework <framework>', 'Test framework')
            .action(async (filePath, options) => {
            await this.ensureInitialized();
            const generateCommand = new generate_1.GenerateCommand(this.core);
            await generateCommand.generateTests(filePath, options);
        }));
        // Register documentation command
        this.program
            .command('docs')
            .description('Generate documentation')
            .addCommand(this.program.createCommand('generate')
            .argument('[path]', 'Path to document', '.')
            .option('-f, --format <format>', 'Output format (md|html|pdf)', 'md')
            .action(async (path, options) => {
            await this.ensureInitialized();
            const docsCommand = new docs_1.DocsCommand(this.core);
            await docsCommand.generateDocs(path, options);
        }));
        // Register deployment command
        this.program
            .command('deploy')
            .description('Deployment assistance')
            .addCommand(this.program.createCommand('plan')
            .argument('<environment>', 'Target environment')
            .action(async (environment, options) => {
            await this.ensureInitialized();
            const deployCommand = new deploy_1.DeployCommand(this.core);
            await deployCommand.createDeploymentPlan(environment, options);
        }));
        // Register monitoring command
        this.program
            .command('monitor')
            .description('Monitor system and learning')
            .addCommand(this.program.createCommand('status')
            .action(async () => {
            await this.ensureInitialized();
            const monitorCommand = new monitor_1.MonitorCommand(this.core);
            await monitorCommand.showStatus();
        }))
            .addCommand(this.program.createCommand('metrics')
            .action(async () => {
            await this.ensureInitialized();
            const monitorCommand = new monitor_1.MonitorCommand(this.core);
            await monitorCommand.showMetrics({});
        }));
        // Register training command
        this.program
            .command('train')
            .description('Training and learning management')
            .addCommand(this.program.createCommand('status')
            .action(async () => {
            await this.ensureInitialized();
            const trainCommand = new train_1.TrainCommand(this.core);
            await trainCommand.showTrainingStatus();
        }))
            .addCommand(this.program.createCommand('technology')
            .argument('<technology>', 'Technology to learn')
            .action(async (technology, options) => {
            await this.ensureInitialized();
            const trainCommand = new train_1.TrainCommand(this.core);
            await trainCommand.trainTechnology(technology, options);
        }));
    }
    async ensureInitialized() {
        if (!this.core || !this.configManager) {
            throw new Error('GOC Agent not initialized. This should not happen.');
        }
    }
    async initialize(options) {
        try {
            // Configure logger
            core_1.logger.updateConfig({
                level: options.verbose ? 'debug' : options.quiet ? 'error' : 'info',
                enableConsole: true
            });
            // Show initialization message
            if (!options.quiet) {
                const spinner = (0, ora_1.default)('Initializing GOC Agent...').start();
                try {
                    // Initialize configuration
                    this.configManager = new core_1.ConfigManager();
                    await this.configManager.initialize();
                    // Initialize core
                    this.core = await (0, core_1.createGocCore)({
                        ai: {
                            defaultProvider: this.configManager.getConfig().ai.defaultProvider,
                            defaultModel: this.configManager.getConfig().ai.defaultModel
                        },
                        global: {
                            logLevel: options.verbose ? 'debug' : 'info'
                        }
                    });
                    spinner.succeed('GOC Agent initialized successfully');
                }
                catch (error) {
                    spinner.fail('Failed to initialize GOC Agent');
                    throw error;
                }
            }
            else {
                // Silent initialization
                this.configManager = new core_1.ConfigManager();
                await this.configManager.initialize();
                this.core = await (0, core_1.createGocCore)({
                    ai: {
                        defaultProvider: this.configManager.getConfig().ai.defaultProvider,
                        defaultModel: this.configManager.getConfig().ai.defaultModel
                    }
                });
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('Failed to initialize GOC Agent:'), error);
            process.exit(1);
        }
    }
    async showStatus() {
        if (!this.core || !this.configManager) {
            console.error(chalk_1.default.red('GOC Agent not initialized'));
            return;
        }
        const config = this.configManager.getConfig();
        const isInitialized = this.core.isInitialized();
        // Check backend connectivity
        const backendHealthy = await this.apiClient.healthCheck();
        const isAuthenticated = this.apiClient.isAuthenticated();
        const statusInfo = [
            ['Status', isInitialized ? chalk_1.default.green('Ready') : chalk_1.default.red('Not Ready')],
            ['Version', '1.0.0'],
            ['Provider', config.ai.defaultProvider],
            ['Model', config.ai.defaultModel],
            ['Backend', backendHealthy ? chalk_1.default.green('Connected') : chalk_1.default.yellow('Offline')],
            ['Authentication', isAuthenticated ? chalk_1.default.green('Authenticated') : chalk_1.default.red('Not Authenticated')],
            ['Config Path', '~/.goc-agent/config.yaml']
        ];
        console.log((0, boxen_1.default)(statusInfo.map(([key, value]) => `${chalk_1.default.bold(key)}: ${value}`).join('\n'), {
            title: 'GOC Agent Status',
            padding: 1,
            borderStyle: 'round',
            borderColor: 'blue'
        }));
        if (!backendHealthy) {
            console.log(chalk_1.default.yellow('\n⚠️  Backend is offline. Some features may not be available.'));
            console.log(chalk_1.default.dim('   Make sure the Laravel backend is running on http://localhost:8000'));
        }
        if (!isAuthenticated && backendHealthy) {
            console.log(chalk_1.default.blue('\n💡 Tip: Use "goc auth login" to authenticate and access cloud features.'));
        }
    }
    async run() {
        try {
            await this.program.parseAsync(process.argv);
        }
        catch (error) {
            core_1.logger.error('CLI execution failed', error);
            console.error(chalk_1.default.red('Error:'), error);
            process.exit(1);
        }
        finally {
            // Cleanup
            if (this.core) {
                await this.core.dispose();
            }
        }
    }
}
// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    core_1.logger.error('Uncaught exception', error);
    console.error(chalk_1.default.red('Uncaught exception:'), error);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    core_1.logger.error('Unhandled rejection', reason);
    console.error(chalk_1.default.red('Unhandled rejection:'), reason);
    process.exit(1);
});
// Run CLI
if (require.main === module) {
    const cli = new GocCLI();
    cli.run();
}
//# sourceMappingURL=cli.js.map