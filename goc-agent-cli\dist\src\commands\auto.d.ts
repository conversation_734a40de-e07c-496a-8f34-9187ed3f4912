/**
 * Auto Mode Command
 *
 * Intelligent autonomous task execution and decision making
 */
import { Command } from 'commander';
import { GocCore } from '@goc-agent/core';
export declare class AutoCommand {
    private core;
    constructor(core: GocCore);
    register(program: Command): void;
    executeGoal(goal: string, options: any): Promise<void>;
    private showStatus;
    private stopExecution;
    private createPlan;
    private makeDecision;
    private getStatusColor;
}
//# sourceMappingURL=auto.d.ts.map