/**
 * Production Configuration Management
 * 
 * Handles environment-specific configuration, API keys, and production settings
 */

import fs from 'fs';
import path from 'path';
import os from 'os';
import chalk from 'chalk';

export interface ProductionConfig {
  environment: 'development' | 'staging' | 'production';
  ai: {
    providers: {
      ollama: {
        enabled: boolean;
        baseUrl: string;
        models: string[];
      };
      goc: {
        enabled: boolean;
        apiKey?: string;
        baseUrl: string;
        tier: 'free' | 'developer' | 'enterprise';
        requestsRemaining?: number;
      };
    };
    defaultProvider: string;
    defaultModel: string;
  };
  features: {
    webResearch: boolean;
    autoTraining: boolean;
    contextEngine: boolean;
    autoMode: boolean;
    telemetry: boolean;
  };
  performance: {
    maxMemoryUsage: number;
    cacheSize: number;
    requestTimeout: number;
    concurrentRequests: number;
  };
  security: {
    encryptApiKeys: boolean;
    validateCertificates: boolean;
    allowInsecureConnections: boolean;
  };
  logging: {
    level: 'error' | 'warn' | 'info' | 'debug';
    enableFileLogging: boolean;
    logDirectory: string;
    maxLogFiles: number;
  };
}

export class ProductionConfigManager {
  private configPath: string;
  private config: ProductionConfig;

  constructor() {
    this.configPath = path.join(os.homedir(), '.goc-agent', 'production.json');
    this.config = this.getDefaultConfig();
  }

  async initialize(): Promise<void> {
    try {
      await this.ensureConfigDirectory();
      await this.loadConfig();
      await this.validateConfig();
      await this.migrateConfig();
    } catch (error) {
      console.error(chalk.red('Failed to initialize production config:'), error);
      throw error;
    }
  }

  private getDefaultConfig(): ProductionConfig {
    return {
      environment: 'production',
      ai: {
        providers: {
          ollama: {
            enabled: true,
            baseUrl: 'http://localhost:11434',
            models: ['llama3.2:3b', 'codellama:7b', 'mistral:7b']
          },
          goc: {
            enabled: true,
            baseUrl: 'https://api.goc-agent.com',
            tier: 'free'
          }
        },
        defaultProvider: 'ollama',
        defaultModel: 'llama3.2:3b'
      },
      features: {
        webResearch: true,
        autoTraining: true,
        contextEngine: true,
        autoMode: true,
        telemetry: false
      },
      performance: {
        maxMemoryUsage: 1024, // MB
        cacheSize: 100, // MB
        requestTimeout: 30000, // ms
        concurrentRequests: 3
      },
      security: {
        encryptApiKeys: true,
        validateCertificates: true,
        allowInsecureConnections: false
      },
      logging: {
        level: 'info',
        enableFileLogging: true,
        logDirectory: path.join(os.homedir(), '.goc-agent', 'logs'),
        maxLogFiles: 10
      }
    };
  }

  private async ensureConfigDirectory(): Promise<void> {
    const configDir = path.dirname(this.configPath);
    if (!fs.existsSync(configDir)) {
      fs.mkdirSync(configDir, { recursive: true });
    }

    // Ensure logs directory
    const logsDir = this.config.logging.logDirectory;
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
  }

  private async loadConfig(): Promise<void> {
    if (fs.existsSync(this.configPath)) {
      try {
        const configData = fs.readFileSync(this.configPath, 'utf8');
        const loadedConfig = JSON.parse(configData);
        this.config = { ...this.config, ...loadedConfig };
      } catch (error) {
        console.warn(chalk.yellow('Failed to load config, using defaults:'), error);
      }
    } else {
      await this.saveConfig();
    }
  }

  private async validateConfig(): Promise<void> {
    // Validate environment
    if (!['development', 'staging', 'production'].includes(this.config.environment)) {
      this.config.environment = 'production';
    }

    // Validate AI providers
    if (!this.config.ai.providers.ollama.enabled && !this.config.ai.providers.goc.enabled) {
      console.warn(chalk.yellow('No AI providers enabled, enabling Ollama by default'));
      this.config.ai.providers.ollama.enabled = true;
    }

    // Validate performance settings
    if (this.config.performance.maxMemoryUsage < 256) {
      this.config.performance.maxMemoryUsage = 256;
    }

    // Validate logging level
    if (!['error', 'warn', 'info', 'debug'].includes(this.config.logging.level)) {
      this.config.logging.level = 'info';
    }
  }

  private async migrateConfig(): Promise<void> {
    // Add any new configuration fields that might be missing
    const defaultConfig = this.getDefaultConfig();
    let needsSave = false;

    // Check for missing fields and add them
    if (!this.config.security) {
      this.config.security = defaultConfig.security;
      needsSave = true;
    }

    if (!this.config.performance) {
      this.config.performance = defaultConfig.performance;
      needsSave = true;
    }

    if (needsSave) {
      await this.saveConfig();
    }
  }

  async saveConfig(): Promise<void> {
    try {
      const configData = JSON.stringify(this.config, null, 2);
      fs.writeFileSync(this.configPath, configData, 'utf8');
    } catch (error) {
      console.error(chalk.red('Failed to save config:'), error);
      throw error;
    }
  }

  getConfig(): ProductionConfig {
    return { ...this.config };
  }

  async updateConfig(updates: Partial<ProductionConfig>): Promise<void> {
    this.config = { ...this.config, ...updates };
    await this.validateConfig();
    await this.saveConfig();
  }

  // Environment-specific methods
  isDevelopment(): boolean {
    return this.config.environment === 'development';
  }

  isProduction(): boolean {
    return this.config.environment === 'production';
  }

  // API Key management
  async setApiKey(provider: string, apiKey: string): Promise<void> {
    if (provider === 'goc') {
      this.config.ai.providers.goc.apiKey = apiKey;
      await this.saveConfig();
    }
  }

  getApiKey(provider: string): string | undefined {
    if (provider === 'goc') {
      return this.config.ai.providers.goc.apiKey;
    }
    return undefined;
  }

  // Feature flags
  isFeatureEnabled(feature: keyof ProductionConfig['features']): boolean {
    return this.config.features[feature];
  }

  async toggleFeature(feature: keyof ProductionConfig['features'], enabled: boolean): Promise<void> {
    this.config.features[feature] = enabled;
    await this.saveConfig();
  }

  // Performance settings
  getPerformanceConfig() {
    return this.config.performance;
  }

  // Security settings
  getSecurityConfig() {
    return this.config.security;
  }

  // Logging configuration
  getLoggingConfig() {
    return this.config.logging;
  }

  // Health check
  async healthCheck(): Promise<{ status: string; issues: string[] }> {
    const issues: string[] = [];

    // Check if config file is writable
    try {
      await this.saveConfig();
    } catch (error) {
      issues.push('Configuration file is not writable');
    }

    // Check if log directory exists and is writable
    try {
      const testFile = path.join(this.config.logging.logDirectory, '.test');
      fs.writeFileSync(testFile, 'test');
      fs.unlinkSync(testFile);
    } catch (error) {
      issues.push('Log directory is not writable');
    }

    // Check memory limits
    const memUsage = process.memoryUsage();
    const memUsageMB = memUsage.heapUsed / 1024 / 1024;
    if (memUsageMB > this.config.performance.maxMemoryUsage * 0.8) {
      issues.push('Memory usage approaching limit');
    }

    return {
      status: issues.length === 0 ? 'healthy' : 'warning',
      issues
    };
  }
}
