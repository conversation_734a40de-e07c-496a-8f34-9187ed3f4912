/**
 * Train Command
 *
 * Manages AI model training and learning
 */
import { Command } from 'commander';
import { GocCore } from '@goc-agent/core';
export declare class TrainCommand {
    private core;
    constructor(core: GocCore);
    register(program: Command): void;
    private startTraining;
    showTrainingStatus(): Promise<void>;
    trainTechnology(technology: string, options: any): Promise<void>;
    private showTrainingHistory;
    private delay;
}
//# sourceMappingURL=train.d.ts.map