/**
 * Analyze Command
 * 
 * Analyzes code files and projects
 */

import { Command } from 'commander';
import { Goc<PERSON>ore } from '@goc-agent/core';
import chalk from 'chalk';
import * as fs from 'fs';
import * as path from 'path';

export class AnalyzeCommand {
  private core: GocCore;

  constructor(core: GocCore) {
    this.core = core;
  }

  register(program: Command): void {
    const analyzeCmd = program
      .command('analyze')
      .description('Analyze code files and projects');

    analyzeCmd
      .command('file <filepath>')
      .description('Analyze a specific file')
      .action(async (filepath: string) => {
        await this.analyzeFile(filepath);
      });

    analyzeCmd
      .command('project [directory]')
      .description('Analyze entire project')
      .action(async (directory: string = '.') => {
        await this.analyzeProject(directory);
      });
  }

  private async analyzeFile(filepath: string): Promise<void> {
    try {
      if (!fs.existsSync(filepath)) {
        console.error(chalk.red('File not found:'), filepath);
        return;
      }

      const content = fs.readFileSync(filepath, 'utf-8');
      const stats = fs.statSync(filepath);
      
      console.log(chalk.blue('File Analysis:'), filepath);
      console.log(chalk.gray('Size:'), this.formatBytes(stats.size));
      console.log(chalk.gray('Lines:'), content.split('\n').length);
      console.log(chalk.gray('Extension:'), path.extname(filepath));
      
      // Basic analysis
      const analysis = this.performBasicAnalysis(content, filepath);
      console.log(chalk.green('Analysis Results:'));
      console.log(analysis);
      
    } catch (error) {
      console.error(chalk.red('Error analyzing file:'), error);
    }
  }

  async analyzeProject(directory: string, options?: any): Promise<void> {
    try {
      if (!fs.existsSync(directory)) {
        console.error(chalk.red('Directory not found:'), directory);
        return;
      }

      console.log(chalk.blue('Project Analysis:'), directory);
      
      const files = this.getCodeFiles(directory);
      console.log(chalk.gray('Code files found:'), files.length);
      
      let totalLines = 0;
      let totalSize = 0;
      
      for (const file of files) {
        const content = fs.readFileSync(file, 'utf-8');
        const stats = fs.statSync(file);
        totalLines += content.split('\n').length;
        totalSize += stats.size;
      }
      
      console.log(chalk.green('Project Summary:'));
      console.log(chalk.gray('Total files:'), files.length);
      console.log(chalk.gray('Total lines:'), totalLines);
      console.log(chalk.gray('Total size:'), this.formatBytes(totalSize));
      
    } catch (error) {
      console.error(chalk.red('Error analyzing project:'), error);
    }
  }

  private performBasicAnalysis(content: string, filepath: string): any {
    const lines = content.split('\n');
    const extension = path.extname(filepath).toLowerCase();
    
    return {
      lineCount: lines.length,
      emptyLines: lines.filter(line => line.trim() === '').length,
      commentLines: this.countCommentLines(lines, extension),
      fileType: this.getFileType(extension),
      complexity: 'Basic' // Placeholder
    };
  }

  private countCommentLines(lines: string[], extension: string): number {
    let count = 0;
    const commentPatterns = this.getCommentPatterns(extension);
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (commentPatterns.some(pattern => trimmed.startsWith(pattern))) {
        count++;
      }
    }
    
    return count;
  }

  private getCommentPatterns(extension: string): string[] {
    switch (extension) {
      case '.js':
      case '.ts':
      case '.jsx':
      case '.tsx':
        return ['//', '/*'];
      case '.py':
        return ['#'];
      case '.php':
        return ['//', '/*', '#'];
      case '.html':
      case '.xml':
        return ['<!--'];
      default:
        return ['//'];
    }
  }

  private getFileType(extension: string): string {
    const types: Record<string, string> = {
      '.js': 'JavaScript',
      '.ts': 'TypeScript',
      '.jsx': 'React JSX',
      '.tsx': 'React TSX',
      '.py': 'Python',
      '.php': 'PHP',
      '.html': 'HTML',
      '.css': 'CSS',
      '.json': 'JSON'
    };
    
    return types[extension] || 'Unknown';
  }

  private getCodeFiles(directory: string): string[] {
    const files: string[] = [];
    const codeExtensions = ['.js', '.ts', '.jsx', '.tsx', '.py', '.php', '.html', '.css', '.json'];
    
    const scan = (dir: string) => {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stats = fs.statSync(fullPath);
        
        if (stats.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scan(fullPath);
        } else if (stats.isFile() && codeExtensions.includes(path.extname(item))) {
          files.push(fullPath);
        }
      }
    };
    
    scan(directory);
    return files;
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
