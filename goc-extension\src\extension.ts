/**
 * GOC Agent VS Code Extension
 *
 * Clean, minimal extension powered by GOC Core
 */

import * as vscode from 'vscode';
import { GocCoreService } from './services/gocCoreService';
import { ChatProvider } from './providers/chatProvider';
import { StatusBarManager } from './services/statusBarManager';
import { CommandManager } from './services/commandManager';
import { FileWatcherService } from './services/fileWatcherService';
import { ResearchProvider } from './providers/researchProvider';
import { ConfigurationManager } from './services/configurationManager';

// Global services - minimal and clean
let gocCoreService: GocCoreService;
let chatProvider: ChatProvider;
let statusBarManager: StatusBarManager;
let commandManager: CommandManager;
let fileWatcherService: FileWatcherService;
let researchProvider: ResearchProvider;
let configurationManager: ConfigurationManager;

/**
 * Clean Extension Activation - Powered by GOC Core
 */
export async function activate(context: vscode.ExtensionContext) {
    try {
        console.log('🚀 Activating GOC Agent extension...');

        // Show progress to user
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Window,
            title: 'Initializing GOC Agent...',
            cancellable: false
        }, async (progress) => {

            // Step 1: Initialize GOC Core Service (all the heavy lifting)
            progress.report({ increment: 30, message: 'Loading AI services...' });
            gocCoreService = new GocCoreService(context);
            await gocCoreService.initialize();

            // Step 2: Initialize minimal UI services
            progress.report({ increment: 30, message: 'Setting up interface...' });
            statusBarManager = new StatusBarManager(gocCoreService);
            chatProvider = new ChatProvider(context, gocCoreService);
            configurationManager = new ConfigurationManager(context);
            commandManager = new CommandManager(context, gocCoreService, chatProvider, statusBarManager, configurationManager);
            fileWatcherService = new FileWatcherService(context, gocCoreService);
            researchProvider = new ResearchProvider(context, gocCoreService);

            // Step 3: Register everything
            progress.report({ increment: 30, message: 'Registering services...' });
            await registerServices(context);

            // Step 4: Final setup
            progress.report({ increment: 10, message: 'Finalizing...' });
            await statusBarManager.refreshStatus();
        });

        // Show success notification with action buttons
        const status = await gocCoreService.getStatus();
        vscode.window.showInformationMessage(
            `🤖 GOC Agent ready! Using ${status.provider} - ${status.model}`,
            'Open Chat', 'View Status', 'Settings'
        ).then(selection => {
            switch (selection) {
                case 'Open Chat':
                    vscode.commands.executeCommand('gocAgent.openChat');
                    break;
                case 'View Status':
                    vscode.commands.executeCommand('gocAgent.showStatus');
                    break;
                case 'Settings':
                    vscode.commands.executeCommand('gocAgent.openSettings');
                    break;
            }
        });

        console.log('✅ GOC Agent extension activated successfully with intelligent features');

    } catch (error) {
        console.error('❌ Failed to activate GOC Agent:', error);

        // Enhanced error handling with recovery options
        const errorMessage = error instanceof Error ? error.message : String(error);
        const isNetworkError = errorMessage.includes('network') || errorMessage.includes('connection');
        const isConfigError = errorMessage.includes('config') || errorMessage.includes('API key');

        let message = `GOC Agent activation failed: ${errorMessage}`;
        let actions: string[] = ['Retry'];

        if (isConfigError) {
            message = `GOC Agent configuration error: ${errorMessage}`;
            actions = ['Open Settings', 'Retry'];
        } else if (isNetworkError) {
            message = `GOC Agent network error: ${errorMessage}`;
            actions = ['Check Connection', 'Retry'];
        }

        actions.push('Report Issue');

        vscode.window.showErrorMessage(message, ...actions).then(selection => {
            switch (selection) {
                case 'Retry':
                    setTimeout(() => activate(context), 3000);
                    break;
                case 'Open Settings':
                    vscode.commands.executeCommand('workbench.action.openSettings', 'gocAgent');
                    break;
                case 'Check Connection':
                    vscode.env.openExternal(vscode.Uri.parse('https://status.goc-agent.com'));
                    break;
                case 'Report Issue':
                    vscode.env.openExternal(vscode.Uri.parse('https://github.com/goc-agent/goc-agent/issues/new'));
                    break;
            }
        });
    }
}

/**
 * Register Services - Clean and Minimal
 */
async function registerServices(context: vscode.ExtensionContext) {
    // Register chat webview provider
    const chatViewProvider = vscode.window.registerWebviewViewProvider(
        ChatProvider.viewType,
        chatProvider,
        {
            webviewOptions: {
                retainContextWhenHidden: true
            }
        }
    );

    // Register configuration change listener
    const configChangeListener = vscode.workspace.onDidChangeConfiguration(
        async (event) => {
            if (event.affectsConfiguration('gocAgent')) {
                // Reinitialize goc-core with new settings
                await gocCoreService.initialize();
                await statusBarManager.refreshStatus();
            }
        }
    );

    // Register file save listener for intelligent context updates
    const fileSaveListener = vscode.workspace.onDidSaveTextDocument(
        async (document) => {
            // Auto-update context when files are saved
            // This uses goc-core's context engine automatically
            console.log(`File saved: ${document.fileName} - Context updated`);
        }
    );

    // Add all disposables to context
    context.subscriptions.push(
        chatViewProvider,
        configChangeListener,
        fileSaveListener,
        statusBarManager,
        fileWatcherService,
        researchProvider
    );
}

/**
 * Clean Extension Deactivation
 */
export async function deactivate() {
    console.log('🔄 Deactivating GOC Agent extension...');

    try {
        // Dispose services in reverse order
        if (researchProvider) {
            researchProvider.dispose();
        }
        if (fileWatcherService) {
            fileWatcherService.dispose();
        }
        if (statusBarManager) {
            statusBarManager.dispose();
        }
        if (gocCoreService) {
            await gocCoreService.dispose();
        }

        console.log('✅ GOC Agent extension deactivated successfully');
    } catch (error) {
        console.error('❌ Error during deactivation:', error);
    }
}
