/**
 * Web Research Command
 * 
 * Advanced web research and content processing using GOC Core
 */

import chalk from 'chalk';
import inquirer from 'inquirer';
import { Command } from 'commander';
import { GocCore } from '@goc-agent/core';

export class WebCommand {
  constructor(private core: GocCore) {}

  register(program: Command): void {
    const webCmd = program
      .command('web')
      .description('Advanced web research and content processing');

    webCmd
      .command('search <query>')
      .description('Search the web with intelligent processing')
      .option('-m, --max <number>', 'Maximum results', '10')
      .option('-l, --language <lang>', 'Search language', 'en')
      .option('-t, --time <range>', 'Time range (day|week|month|year|all)', 'all')
      .option('--content', 'Include full content extraction')
      .option('--safe', 'Enable safe search')
      .action(async (query, options) => {
        await this.searchWeb(query, options);
      });

    webCmd
      .command('extract <url>')
      .description('Extract and process content from a URL')
      .option('--summary', 'Include content summary')
      .option('--code', 'Extract code examples')
      .option('--patterns', 'Analyze patterns')
      .action(async (url, options) => {
        await this.extractContent(url, options);
      });

    webCmd
      .command('research <topic>')
      .description('Research a technology or topic comprehensively')
      .option('-d, --depth <level>', 'Research depth (basic|intermediate|advanced)', 'intermediate')
      .option('-s, --sources <number>', 'Maximum sources', '10')
      .option('--examples', 'Include code examples')
      .option('--trends', 'Include trend analysis')
      .action(async (topic, options) => {
        await this.researchTopic(topic, options);
      });

    webCmd
      .command('trends <topics...>')
      .description('Monitor technology trends')
      .option('-t, --timeframe <period>', 'Timeframe (daily|weekly|monthly)', 'weekly')
      .option('--alerts', 'Include trend alerts')
      .action(async (topics, options) => {
        await this.monitorTrends(topics, options);
      });

    webCmd
      .command('insights')
      .description('Show learning insights and recommendations')
      .action(async () => {
        await this.showInsights();
      });

    webCmd
      .command('summarize <url>')
      .description('Summarize content from a URL')
      .action(async (url) => {
        await this.summarizeContent(url);
      });
  }

  async searchWeb(query: string, options: any): Promise<void> {
    try {
      console.log(chalk.blue(`🔍 Searching: "${query}"`));
      console.log(chalk.dim('─'.repeat(60)));

      const webResearcher = this.core.getWebResearcher();
      const results = await webResearcher.search(query, {
        maxResults: parseInt(options.max),
        language: options.language,
        timeRange: options.time,
        safeSearch: options.safe,
        includeContent: options.content
      });

      if (results.length === 0) {
        console.log(chalk.yellow('No results found.'));
        return;
      }

      console.log(chalk.blue(`📋 Found ${results.length} results:`));
      console.log();

      results.forEach((result, index) => {
        const score = result.relevanceScore ? ` (${(result.relevanceScore * 100).toFixed(0)}%)` : '';
        console.log(chalk.bold(`${index + 1}. ${result.title}${score}`));
        console.log(chalk.dim(`   ${result.url}`));
        console.log(`   ${result.snippet}`);
        
        if (result.content && options.content) {
          const preview = result.content.substring(0, 200);
          console.log(chalk.dim(`   Content: ${preview}${result.content.length > 200 ? '...' : ''}`));
        }
        
        if (result.publishedDate) {
          console.log(chalk.dim(`   Published: ${result.publishedDate.toLocaleDateString()}`));
        }
        
        console.log();
      });

      console.log(chalk.dim(`Search completed in ${results.length} results from ${results[0]?.source || 'web'}`));
    } catch (error) {
      console.error(chalk.red('❌ Search error:'), error);
    }
  }

  private async extractContent(url: string, options: any): Promise<void> {
    try {
      console.log(chalk.blue(`📄 Extracting content from: ${url}`));
      console.log(chalk.dim('─'.repeat(60)));

      const webResearcher = this.core.getWebResearcher();
      const result = await webResearcher.extractContent(url, {
        includeSummary: options.summary,
        extractCode: options.code,
        analyzePatterns: options.patterns
      });

      console.log(chalk.bold(`Title: ${result.title}`));
      console.log();

      if (result.summary && options.summary) {
        console.log(chalk.blue('📝 Summary:'));
        console.log(result.summary);
        console.log();
      }

      console.log(chalk.blue('📊 Metadata:'));
      console.log(`Word Count: ${result.metadata.wordCount}`);
      console.log(`Reading Time: ${result.metadata.readingTime} minutes`);
      console.log(`Language: ${result.metadata.language}`);
      
      if (result.metadata.author) {
        console.log(`Author: ${result.metadata.author}`);
      }
      
      if (result.metadata.publishedDate) {
        console.log(`Published: ${result.metadata.publishedDate.toLocaleDateString()}`);
      }

      if (result.metadata.tags && result.metadata.tags.length > 0) {
        console.log(`Tags: ${result.metadata.tags.join(', ')}`);
      }

      if (result.codeExamples.length > 0 && options.code) {
        console.log(chalk.blue('\n💻 Code Examples:'));
        result.codeExamples.forEach((example, index) => {
          console.log(chalk.bold(`${index + 1}. ${example.language.toUpperCase()}`));
          if (example.description) {
            console.log(chalk.dim(`   ${example.description}`));
          }
          console.log('```' + example.language);
          console.log(example.code.substring(0, 300) + (example.code.length > 300 ? '\n   ...' : ''));
          console.log('```');
          console.log();
        });
      }

      if (result.links.length > 0) {
        console.log(chalk.blue('\n🔗 Links:'));
        result.links.slice(0, 10).forEach((link, index) => {
          const typeColor = link.type === 'internal' ? chalk.green : chalk.blue;
          console.log(`${index + 1}. ${typeColor(link.type)} ${link.title}`);
          console.log(chalk.dim(`   ${link.url}`));
        });
      }

      console.log(chalk.dim(`\nContent extraction completed successfully`));
    } catch (error) {
      console.error(chalk.red('❌ Content extraction error:'), error);
    }
  }

  async researchTopic(topic: string, options: any): Promise<void> {
    try {
      console.log(chalk.blue(`🔬 Researching: "${topic}"`));
      console.log(chalk.dim(`Depth: ${options.depth}, Max Sources: ${options.sources}`));
      console.log(chalk.dim('─'.repeat(60)));

      const webResearcher = this.core.getWebResearcher();
      const result = await webResearcher.researchTopic(topic, {
        depth: options.depth,
        maxSources: parseInt(options.sources),
        includeExamples: options.examples,
        includeTrends: options.trends
      });

      console.log(chalk.blue('📋 Research Summary:'));
      console.log(result.summary);
      console.log();

      if (result.keyPoints.length > 0) {
        console.log(chalk.blue('🔑 Key Points:'));
        result.keyPoints.forEach((point, index) => {
          console.log(`${index + 1}. ${point}`);
        });
        console.log();
      }

      if (result.codeExamples.length > 0 && options.examples) {
        console.log(chalk.blue('💻 Code Examples:'));
        result.codeExamples.slice(0, 5).forEach((example, index) => {
          console.log(chalk.bold(`${index + 1}. ${example.language.toUpperCase()} - ${example.description}`));
          console.log(chalk.dim(`   Source: ${example.source}`));
          console.log('```' + example.language);
          console.log(example.code.substring(0, 200) + (example.code.length > 200 ? '\n   ...' : ''));
          console.log('```');
          console.log();
        });
      }

      if (result.trends && result.trends.length > 0 && options.trends) {
        console.log(chalk.blue('📈 Trends:'));
        result.trends.forEach((trend, index) => {
          const trendColor = trend.trend === 'rising' ? chalk.green : 
                           trend.trend === 'stable' ? chalk.yellow : chalk.red;
          console.log(`${index + 1}. ${trend.topic} - ${trendColor(trend.trend)} (${(trend.confidence * 100).toFixed(0)}%)`);
          console.log(chalk.dim(`   Timeframe: ${trend.timeframe}`));
          if (trend.relatedTopics.length > 0) {
            console.log(chalk.dim(`   Related: ${trend.relatedTopics.join(', ')}`));
          }
        });
        console.log();
      }

      if (result.relatedTopics.length > 0) {
        console.log(chalk.blue('🔗 Related Topics:'));
        console.log(result.relatedTopics.join(', '));
        console.log();
      }

      console.log(chalk.blue('📚 Sources:'));
      result.sources.slice(0, 5).forEach((source, index) => {
        console.log(`${index + 1}. ${source.title}`);
        console.log(chalk.dim(`   ${source.url}`));
      });

      console.log(chalk.dim(`\nResearch completed with ${result.sources.length} sources analyzed`));
    } catch (error) {
      console.error(chalk.red('❌ Research error:'), error);
    }
  }

  private async monitorTrends(topics: string[], options: any): Promise<void> {
    try {
      console.log(chalk.blue(`📈 Monitoring trends for: ${topics.join(', ')}`));
      console.log(chalk.dim(`Timeframe: ${options.timeframe}`));
      console.log(chalk.dim('─'.repeat(60)));

      // For now, show a simplified trend analysis
      console.log(chalk.yellow('⚠️ Trend monitoring is a placeholder implementation'));
      console.log('Real trend monitoring would analyze:');
      console.log('• Search volume changes');
      console.log('• GitHub activity');
      console.log('• Stack Overflow questions');
      console.log('• Job posting trends');
      console.log('• Social media mentions');
      console.log();

      topics.forEach((topic, index) => {
        console.log(chalk.bold(`${index + 1}. ${topic}`));
        console.log(chalk.green('   Status: Rising (simulated)'));
        console.log(chalk.dim('   Confidence: 75%'));
        console.log(chalk.dim('   Sources: Web research, GitHub, Stack Overflow'));
        console.log();
      });

      console.log(chalk.dim('Trend monitoring completed'));
    } catch (error) {
      console.error(chalk.red('❌ Trend monitoring error:'), error);
    }
  }

  private async showInsights(): Promise<void> {
    try {
      console.log(chalk.blue('🧠 Learning Insights & Recommendations'));
      console.log(chalk.dim('─'.repeat(60)));

      const webResearcher = this.core.getWebResearcher();
      const insights = await webResearcher.getLearningInsights();

      if (insights.topSearches.length > 0) {
        console.log(chalk.blue('🔍 Top Searches:'));
        insights.topSearches.forEach((search, index) => {
          console.log(`${index + 1}. "${search.query}" (${search.count} times)`);
        });
        console.log();
      }

      if (insights.discoveredPatterns.length > 0) {
        console.log(chalk.blue('🎨 Discovered Patterns:'));
        insights.discoveredPatterns.slice(0, 10).forEach((pattern, index) => {
          console.log(`${index + 1}. ${pattern.pattern} (frequency: ${pattern.frequency})`);
        });
        console.log();
      }

      if (insights.recommendedTopics.length > 0) {
        console.log(chalk.blue('💡 Recommended Topics:'));
        insights.recommendedTopics.forEach((topic, index) => {
          console.log(`${index + 1}. ${topic}`);
        });
        console.log();
      }

      if (insights.trendingTechnologies.length > 0) {
        console.log(chalk.blue('🚀 Trending Technologies:'));
        insights.trendingTechnologies.forEach((tech, index) => {
          console.log(`${index + 1}. ${tech}`);
        });
        console.log();
      }

      if (insights.topSearches.length === 0 && insights.discoveredPatterns.length === 0) {
        console.log(chalk.yellow('No learning data available yet. Start using web research to build insights!'));
      }

      console.log(chalk.dim('Learning insights generated from web research activity'));
    } catch (error) {
      console.error(chalk.red('❌ Insights error:'), error);
    }
  }

  private async summarizeContent(url: string): Promise<void> {
    try {
      console.log(chalk.blue(`📝 Summarizing content from: ${url}`));
      console.log(chalk.dim('─'.repeat(60)));

      const webResearcher = this.core.getWebResearcher();
      const content = await webResearcher.fetchContent(url);
      const summary = await webResearcher.summarizeContent(content.content);

      console.log(chalk.bold(`Title: ${content.title}`));
      console.log();
      console.log(chalk.blue('📝 Summary:'));
      console.log(summary);
      console.log();
      console.log(chalk.dim(`Original content: ${content.content.length} characters`));
      console.log(chalk.dim(`Summary: ${summary.length} characters`));
      console.log(chalk.dim(`Compression ratio: ${((1 - summary.length / content.content.length) * 100).toFixed(1)}%`));
    } catch (error) {
      console.error(chalk.red('❌ Summarization error:'), error);
    }
  }
}
