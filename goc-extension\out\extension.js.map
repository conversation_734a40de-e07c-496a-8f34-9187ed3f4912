{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAAiC;AACjC,8DAA2D;AAC3D,2DAAwD;AACxD,kEAA+D;AAC/D,8DAA2D;AAC3D,sEAAmE;AACnE,mEAAgE;AAChE,0EAAuE;AAEvE,sCAAsC;AACtC,IAAI,cAA8B,CAAC;AACnC,IAAI,YAA0B,CAAC;AAC/B,IAAI,gBAAkC,CAAC;AACvC,IAAI,cAA8B,CAAC;AACnC,IAAI,kBAAsC,CAAC;AAC3C,IAAI,gBAAkC,CAAC;AACvC,IAAI,oBAA0C,CAAC;AAE/C;;GAEG;AACI,KAAK,UAAU,QAAQ,CAAC,OAAgC;IAC3D,IAAI;QACA,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QAEpD,wBAAwB;QACxB,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC7B,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,MAAM;YACxC,KAAK,EAAE,2BAA2B;YAClC,WAAW,EAAE,KAAK;SACrB,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;YAElB,8DAA8D;YAC9D,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC,CAAC;YACtE,cAAc,GAAG,IAAI,+BAAc,CAAC,OAAO,CAAC,CAAC;YAC7C,MAAM,cAAc,CAAC,UAAU,EAAE,CAAC;YAElC,yCAAyC;YACzC,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAC,CAAC;YACvE,gBAAgB,GAAG,IAAI,mCAAgB,CAAC,cAAc,CAAC,CAAC;YACxD,YAAY,GAAG,IAAI,2BAAY,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;YACzD,oBAAoB,GAAG,IAAI,2CAAoB,CAAC,OAAO,CAAC,CAAC;YACzD,cAAc,GAAG,IAAI,+BAAc,CAAC,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,gBAAgB,EAAE,oBAAoB,CAAC,CAAC;YACnH,kBAAkB,GAAG,IAAI,uCAAkB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;YACrE,gBAAgB,GAAG,IAAI,mCAAgB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;YAEjE,8BAA8B;YAC9B,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAC,CAAC;YACvE,MAAM,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAEhC,sBAAsB;YACtB,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC;YAC7D,MAAM,gBAAgB,CAAC,aAAa,EAAE,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,gDAAgD;QAChD,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,SAAS,EAAE,CAAC;QAChD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,6BAA6B,MAAM,CAAC,QAAQ,MAAM,MAAM,CAAC,KAAK,EAAE,EAChE,WAAW,EAAE,aAAa,EAAE,UAAU,CACzC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACf,QAAQ,SAAS,EAAE;gBACf,KAAK,WAAW;oBACZ,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;oBACpD,MAAM;gBACV,KAAK,aAAa;oBACd,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;oBACtD,MAAM;gBACV,KAAK,UAAU;oBACX,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC;oBACxD,MAAM;aACb;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,wEAAwE,CAAC,CAAC;KAEzF;IAAC,OAAO,KAAK,EAAE;QACZ,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QAExD,gDAAgD;QAChD,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,MAAM,cAAc,GAAG,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAC/F,MAAM,aAAa,GAAG,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAE1F,IAAI,OAAO,GAAG,gCAAgC,YAAY,EAAE,CAAC;QAC7D,IAAI,OAAO,GAAa,CAAC,OAAO,CAAC,CAAC;QAElC,IAAI,aAAa,EAAE;YACf,OAAO,GAAG,kCAAkC,YAAY,EAAE,CAAC;YAC3D,OAAO,GAAG,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;SACxC;aAAM,IAAI,cAAc,EAAE;YACvB,OAAO,GAAG,4BAA4B,YAAY,EAAE,CAAC;YACrD,OAAO,GAAG,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;SAC3C;QAED,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAE7B,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACjE,QAAQ,SAAS,EAAE;gBACf,KAAK,OAAO;oBACR,UAAU,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC;oBAC1C,MAAM;gBACV,KAAK,eAAe;oBAChB,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,EAAE,UAAU,CAAC,CAAC;oBAC5E,MAAM;gBACV,KAAK,kBAAkB;oBACnB,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC,CAAC;oBAC1E,MAAM;gBACV,KAAK,cAAc;oBACf,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,mDAAmD,CAAC,CAAC,CAAC;oBAC/F,MAAM;aACb;QACL,CAAC,CAAC,CAAC;KACN;AACL,CAAC;AA7FD,4BA6FC;AAED;;GAEG;AACH,KAAK,UAAU,gBAAgB,CAAC,OAAgC;IAC5D,iCAAiC;IACjC,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAC9D,2BAAY,CAAC,QAAQ,EACrB,YAAY,EACZ;QACI,cAAc,EAAE;YACZ,uBAAuB,EAAE,IAAI;SAChC;KACJ,CACJ,CAAC;IAEF,yCAAyC;IACzC,MAAM,oBAAoB,GAAG,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAClE,KAAK,EAAE,KAAK,EAAE,EAAE;QACZ,IAAI,KAAK,CAAC,oBAAoB,CAAC,UAAU,CAAC,EAAE;YACxC,0CAA0C;YAC1C,MAAM,cAAc,CAAC,UAAU,EAAE,CAAC;YAClC,MAAM,gBAAgB,CAAC,aAAa,EAAE,CAAC;SAC1C;IACL,CAAC,CACJ,CAAC;IAEF,8DAA8D;IAC9D,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAC3D,KAAK,EAAE,QAAQ,EAAE,EAAE;QACf,2CAA2C;QAC3C,oDAAoD;QACpD,OAAO,CAAC,GAAG,CAAC,eAAe,QAAQ,CAAC,QAAQ,oBAAoB,CAAC,CAAC;IACtE,CAAC,CACJ,CAAC;IAEF,iCAAiC;IACjC,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,gBAAgB,EAChB,oBAAoB,EACpB,gBAAgB,EAChB,gBAAgB,EAChB,kBAAkB,EAClB,gBAAgB,CACnB,CAAC;AACN,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,UAAU;IAC5B,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IAEtD,IAAI;QACA,oCAAoC;QACpC,IAAI,gBAAgB,EAAE;YAClB,gBAAgB,CAAC,OAAO,EAAE,CAAC;SAC9B;QACD,IAAI,kBAAkB,EAAE;YACpB,kBAAkB,CAAC,OAAO,EAAE,CAAC;SAChC;QACD,IAAI,gBAAgB,EAAE;YAClB,gBAAgB,CAAC,OAAO,EAAE,CAAC;SAC9B;QACD,IAAI,cAAc,EAAE;YAChB,MAAM,cAAc,CAAC,OAAO,EAAE,CAAC;SAClC;QAED,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;KACjE;IAAC,OAAO,KAAK,EAAE;QACZ,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;KACxD;AACL,CAAC;AAtBD,gCAsBC"}