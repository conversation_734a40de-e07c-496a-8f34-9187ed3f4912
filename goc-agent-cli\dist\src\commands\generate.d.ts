/**
 * Code Generation Command
 *
 * Intelligent code generation with pattern recognition and best practices
 */
import { Command } from 'commander';
import { GocCore } from '@goc-agent/core';
export declare class GenerateCommand {
    private core;
    constructor(core: GocCore);
    register(program: Command): void;
    generateCode(description: string, options: any): Promise<void>;
    private refactorCode;
    private generateFromDescription;
    private listTemplates;
    private interactiveGeneration;
    private saveGeneratedCode;
    private getFileExtension;
    private getTestFilePath;
    private detectLanguage;
    private detectProjectType;
    generateTests(filePath: string, options: any): Promise<void>;
}
//# sourceMappingURL=generate.d.ts.map