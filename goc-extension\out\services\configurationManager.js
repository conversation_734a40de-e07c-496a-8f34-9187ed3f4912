"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigurationManager = void 0;
const vscode = __importStar(require("vscode"));
const node_fetch_1 = __importDefault(require("node-fetch"));
const backendApiClient_1 = require("./backendApiClient");
class ConfigurationManager {
    constructor(context) {
        this.providers = [
            {
                name: 'ollama',
                displayName: 'Ollama (Local)',
                models: [],
                requiresApiKey: false,
                defaultModel: 'llama3.2:3b'
            },
            {
                name: 'goc',
                displayName: 'GOC Agent Model',
                models: ['goc-agent-cloud', 'goc-agent-dev'],
                requiresApiKey: true,
                defaultModel: 'goc-agent-cloud'
            }
            // Other providers hidden for now
            // {
            //     name: 'openai',
            //     displayName: 'OpenAI',
            //     models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
            //     requiresApiKey: true,
            //     defaultModel: 'gpt-4'
            // },
            // {
            //     name: 'groq',
            //     displayName: 'Groq',
            //     models: ['llama-3.1-70b-versatile', 'llama-3.1-8b-instant'],
            //     requiresApiKey: true,
            //     defaultModel: 'llama-3.1-70b-versatile'
            // },
            // {
            //     name: 'gemini',
            //     displayName: 'Google Gemini',
            //     models: ['gemini-pro', 'gemini-1.5-pro'],
            //     requiresApiKey: true,
            //     defaultModel: 'gemini-pro'
            // },
            // {
            //     name: 'claude',
            //     displayName: 'Anthropic Claude',
            //     models: ['claude-3-sonnet-20240229', 'claude-3-haiku-20240307'],
            //     requiresApiKey: true,
            //     defaultModel: 'claude-3-sonnet-20240229'
            // }
        ];
        this.configuration = this.loadConfiguration();
        this.backendClient = new backendApiClient_1.BackendAPIClient(context);
    }
    getConfiguration() {
        return { ...this.configuration };
    }
    async getProviders() {
        // Refresh Ollama models dynamically
        await this.refreshOllamaModels();
        return [...this.providers];
    }
    async refreshOllamaModels() {
        try {
            const ollamaProvider = this.providers.find(p => p.name === 'ollama');
            if (!ollamaProvider)
                return;
            const models = await this.fetchOllamaModels();
            ollamaProvider.models = models;
            if (models.length === 0) {
                // Show helpful message if no models are installed
                vscode.window.showWarningMessage('No Ollama models found. Install models with: ollama pull llama3.2:3b', 'Install Models', 'Learn More').then(selection => {
                    if (selection === 'Install Models') {
                        vscode.env.openExternal(vscode.Uri.parse('https://ollama.ai/library'));
                    }
                    else if (selection === 'Learn More') {
                        vscode.env.openExternal(vscode.Uri.parse('https://ollama.ai/download'));
                    }
                });
            }
        }
        catch (error) {
            console.warn('Failed to refresh Ollama models:', error);
        }
    }
    async fetchOllamaModels() {
        try {
            const baseUrl = 'http://localhost:11434';
            const response = await (0, node_fetch_1.default)(`${baseUrl}/api/tags`, {
                method: 'GET',
                signal: AbortSignal.timeout(5000)
            });
            if (response.ok) {
                const data = await response.json();
                const models = data.models?.map((model) => model.name) || [];
                // Sort models with recommended ones first
                const recommendedOrder = [
                    'llama3.2:3b',
                    'llama3.2:1b',
                    'codellama:7b',
                    'codellama:13b',
                    'mistral:7b',
                    'deepseek-coder:6.7b',
                    'qwen2.5-coder:7b'
                ];
                return models.sort((a, b) => {
                    const aIndex = recommendedOrder.indexOf(a);
                    const bIndex = recommendedOrder.indexOf(b);
                    if (aIndex !== -1 && bIndex !== -1) {
                        return aIndex - bIndex;
                    }
                    else if (aIndex !== -1) {
                        return -1;
                    }
                    else if (bIndex !== -1) {
                        return 1;
                    }
                    else {
                        return a.localeCompare(b);
                    }
                });
            }
            else {
                return [];
            }
        }
        catch (error) {
            console.warn('Ollama not available:', error);
            return [];
        }
    }
    getProvider(name) {
        return this.providers.find(p => p.name === name);
    }
    getCurrentProvider() {
        return this.getProvider(this.configuration.provider);
    }
    async setProvider(providerName) {
        const provider = this.getProvider(providerName);
        if (!provider) {
            throw new Error(`Unknown provider: ${providerName}`);
        }
        const config = vscode.workspace.getConfiguration(ConfigurationManager.CONFIG_SECTION);
        await config.update('provider', providerName, vscode.ConfigurationTarget.Global);
        // Set default model for the provider
        await config.update('model', provider.defaultModel, vscode.ConfigurationTarget.Global);
        this.reloadConfiguration();
    }
    async setModel(modelName) {
        const currentProvider = this.getCurrentProvider();
        if (!currentProvider) {
            throw new Error('No provider selected');
        }
        if (!currentProvider.models.includes(modelName)) {
            throw new Error(`Model ${modelName} not available for provider ${currentProvider.name}`);
        }
        const config = vscode.workspace.getConfiguration(ConfigurationManager.CONFIG_SECTION);
        await config.update('model', modelName, vscode.ConfigurationTarget.Global);
        this.reloadConfiguration();
    }
    async setApiKey(apiKey) {
        const config = vscode.workspace.getConfiguration(ConfigurationManager.CONFIG_SECTION);
        await config.update('apiKey', apiKey, vscode.ConfigurationTarget.Global);
        this.reloadConfiguration();
    }
    async updateSetting(key, value) {
        const config = vscode.workspace.getConfiguration(ConfigurationManager.CONFIG_SECTION);
        await config.update(key, value, vscode.ConfigurationTarget.Global);
        this.reloadConfiguration();
    }
    reloadConfiguration() {
        this.configuration = this.loadConfiguration();
    }
    loadConfiguration() {
        const config = vscode.workspace.getConfiguration(ConfigurationManager.CONFIG_SECTION);
        return {
            provider: config.get('provider') || 'ollama',
            model: config.get('model') || 'llama3.2:3b',
            apiKey: config.get('apiKey') || '',
            autoTraining: config.get('autoTraining') ?? true,
            webResearch: config.get('webResearch') ?? true,
            contextLines: config.get('contextLines') || 50,
            showProgress: config.get('showProgress') ?? true,
            autoSave: config.get('autoSave') ?? false,
            autoAnalysis: config.get('autoAnalysis') ?? false
        };
    }
    validateConfiguration() {
        const errors = [];
        const config = this.getConfiguration();
        // Check if provider exists
        const provider = this.getProvider(config.provider);
        if (!provider) {
            errors.push(`Unknown provider: ${config.provider}`);
        }
        else {
            // Check if model is valid for provider
            if (!provider.models.includes(config.model)) {
                errors.push(`Model ${config.model} not available for provider ${provider.name}`);
            }
            // Check API key if required
            if (provider.requiresApiKey && !config.apiKey) {
                errors.push(`API key required for provider ${provider.name}`);
            }
        }
        // Validate numeric settings
        if (config.contextLines < 10 || config.contextLines > 200) {
            errors.push('Context lines must be between 10 and 200');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    async promptForApiKey(providerName) {
        const provider = providerName ? this.getProvider(providerName) : this.getCurrentProvider();
        if (!provider) {
            return undefined;
        }
        const apiKey = await vscode.window.showInputBox({
            prompt: `Enter API key for ${provider.displayName}`,
            password: true,
            placeHolder: 'Your API key...',
            validateInput: (value) => {
                if (!value || value.trim().length === 0) {
                    return 'API key cannot be empty';
                }
                return null;
            }
        });
        if (apiKey) {
            await this.setApiKey(apiKey);
        }
        return apiKey;
    }
    async selectProvider() {
        const items = this.providers.map(provider => {
            let description = '';
            let detail = '';
            if (provider.name === 'ollama') {
                description = 'Free local AI models';
                detail = 'No registration required - Install Ollama and pull models locally';
            }
            else if (provider.name === 'goc') {
                description = 'Hosted AI models';
                detail = 'Registration required - 50 free requests/month, $29/month for 500 requests';
            }
            return {
                label: provider.displayName,
                description,
                detail,
                value: provider.name
            };
        });
        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: 'Choose between local (Ollama) or hosted (GOC Agent) models',
            matchOnDescription: true,
            matchOnDetail: true
        });
        if (selected) {
            await this.setProvider(selected.value);
            // Handle authentication for GOC Agent
            if (selected.value === 'goc') {
                await this.handleGocAgentAuth();
            }
        }
        return selected?.value;
    }
    async selectModel() {
        const currentProvider = this.getCurrentProvider();
        if (!currentProvider) {
            vscode.window.showErrorMessage('Please select a provider first');
            return undefined;
        }
        if (currentProvider.name === 'ollama') {
            return await this.selectOllamaModel();
        }
        else if (currentProvider.name === 'goc') {
            return await this.selectGocAgentModel();
        }
        return undefined;
    }
    async selectOllamaModel() {
        // Refresh models for Ollama
        await this.refreshOllamaModels();
        const currentProvider = this.getCurrentProvider();
        if (currentProvider.models.length === 0) {
            await this.showOllamaSetupDialog();
            return undefined;
        }
        const items = currentProvider.models.map(model => ({
            label: this.formatOllamaModelName(model),
            description: model === this.configuration.model ? '(current)' : this.getOllamaModelDescription(model),
            detail: `Local model - ${this.getOllamaModelSize(model)}`,
            value: model
        }));
        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: `Select Ollama model (${currentProvider.models.length} installed)`,
            matchOnDescription: true
        });
        if (selected) {
            await this.setModel(selected.value);
        }
        return selected?.value;
    }
    async selectGocAgentModel() {
        const items = [
            {
                label: 'GOC Agent Model (Free)',
                description: '50 requests/month forever',
                detail: 'Perfect for trying GOC Agent - No payment required',
                value: 'goc-agent-cloud'
            },
            {
                label: 'GOC Agent Model (Developer)',
                description: '$29/month for 500 requests',
                detail: 'Full-featured model for professional development',
                value: 'goc-agent-dev'
            }
        ];
        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: 'Select GOC Agent model tier',
            matchOnDescription: true
        });
        if (selected) {
            await this.setModel(selected.value);
            // Show authentication info if not authenticated
            if (!this.configuration.apiKey) {
                await this.handleGocAgentAuth();
            }
        }
        return selected?.value;
    }
    getStatusText() {
        const provider = this.getCurrentProvider();
        if (!provider) {
            return 'No Provider';
        }
        if (provider.name === 'ollama') {
            return `Ollama: ${this.formatOllamaModelName(this.configuration.model)}`;
        }
        else if (provider.name === 'goc') {
            const modelName = this.configuration.model === 'goc-agent-cloud' ? 'Free' : 'Developer';
            return `GOC Agent: ${modelName}`;
        }
        return `${provider.displayName}: ${this.configuration.model}`;
    }
    formatOllamaModelName(modelId) {
        const nameMap = {
            'llama3.2:3b': 'Llama 3.2 3B',
            'llama3.2:1b': 'Llama 3.2 1B',
            'codellama:7b': 'Code Llama 7B',
            'codellama:13b': 'Code Llama 13B',
            'mistral:7b': 'Mistral 7B',
            'deepseek-coder:6.7b': 'DeepSeek Coder 6.7B',
            'qwen2.5-coder:7b': 'Qwen2.5 Coder 7B'
        };
        return nameMap[modelId] || modelId;
    }
    getOllamaModelDescription(modelId) {
        const descMap = {
            'llama3.2:3b': 'Recommended for most tasks',
            'llama3.2:1b': 'Lightweight and fast',
            'codellama:7b': 'Specialized for coding',
            'codellama:13b': 'Advanced coding model',
            'mistral:7b': 'General purpose',
            'deepseek-coder:6.7b': 'Optimized for code',
            'qwen2.5-coder:7b': 'Latest coding model'
        };
        return descMap[modelId] || 'Local AI model';
    }
    getOllamaModelSize(modelId) {
        const sizeMap = {
            'llama3.2:3b': '2.0GB',
            'llama3.2:1b': '1.3GB',
            'codellama:7b': '3.8GB',
            'codellama:13b': '7.3GB',
            'mistral:7b': '4.1GB',
            'deepseek-coder:6.7b': '3.8GB',
            'qwen2.5-coder:7b': '4.2GB'
        };
        return sizeMap[modelId] || 'Unknown size';
    }
    async handleGocAgentAuth() {
        const isAuthenticated = this.backendClient.isAuthenticated();
        if (isAuthenticated) {
            // Check subscription status
            const access = await this.backendClient.validateGocAgentAccess();
            if (access.hasAccess) {
                vscode.window.showInformationMessage(`✅ Authenticated! Plan: ${access.plan} | Requests remaining: ${access.requestsRemaining}`);
                return;
            }
            else {
                vscode.window.showWarningMessage(`⚠️ ${access.message} | Plan: ${access.plan}`, 'Upgrade Plan', 'View Usage').then(selection => {
                    if (selection === 'Upgrade Plan') {
                        vscode.env.openExternal(vscode.Uri.parse('https://goc-agent.com/pricing'));
                    }
                    else if (selection === 'View Usage') {
                        this.showUserProfile();
                    }
                });
                return;
            }
        }
        const action = await vscode.window.showInformationMessage('GOC Agent models require authentication. Choose an option:', 'Register New Account', 'Login Existing Account', 'Learn More', 'Cancel');
        if (action === 'Register New Account') {
            await this.showRegistrationDialog();
        }
        else if (action === 'Login Existing Account') {
            await this.showLoginDialog();
        }
        else if (action === 'Learn More') {
            vscode.env.openExternal(vscode.Uri.parse('https://goc-agent.com/pricing'));
        }
    }
    async showRegistrationDialog() {
        const name = await vscode.window.showInputBox({
            prompt: 'Enter your full name',
            placeHolder: 'John Doe',
            validateInput: (value) => {
                if (!value || value.trim().length < 2) {
                    return 'Name must be at least 2 characters';
                }
                return null;
            }
        });
        if (!name)
            return;
        const email = await vscode.window.showInputBox({
            prompt: 'Enter your email address',
            placeHolder: '<EMAIL>',
            validateInput: (value) => {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!value || !emailRegex.test(value)) {
                    return 'Please enter a valid email address';
                }
                return null;
            }
        });
        if (!email)
            return;
        const password = await vscode.window.showInputBox({
            prompt: 'Create a password (minimum 6 characters)',
            password: true,
            validateInput: (value) => {
                if (!value || value.length < 6) {
                    return 'Password must be at least 6 characters';
                }
                return null;
            }
        });
        if (!password)
            return;
        vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: 'Creating account...',
            cancellable: false
        }, async () => {
            const result = await this.backendClient.register(name, email, password);
            if (result.success) {
                vscode.window.showInformationMessage(`✅ Welcome to GOC Agent, ${result.user?.name}! You now have 50 free requests per month.`, 'View Profile').then(selection => {
                    if (selection === 'View Profile') {
                        this.showUserProfile();
                    }
                });
            }
            else {
                vscode.window.showErrorMessage(`❌ Registration failed: ${result.message}`);
            }
        });
    }
    async showLoginDialog() {
        const email = await vscode.window.showInputBox({
            prompt: 'Enter your email address',
            placeHolder: '<EMAIL>',
            validateInput: (value) => {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!value || !emailRegex.test(value)) {
                    return 'Please enter a valid email address';
                }
                return null;
            }
        });
        if (!email)
            return;
        const password = await vscode.window.showInputBox({
            prompt: 'Enter your password',
            password: true,
            validateInput: (value) => {
                if (!value || value.length === 0) {
                    return 'Password cannot be empty';
                }
                return null;
            }
        });
        if (!password)
            return;
        vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: 'Signing in...',
            cancellable: false
        }, async () => {
            const result = await this.backendClient.login(email, password);
            if (result.success) {
                vscode.window.showInformationMessage(`✅ Welcome back, ${result.user?.name}!`, 'View Profile').then(selection => {
                    if (selection === 'View Profile') {
                        this.showUserProfile();
                    }
                });
            }
            else {
                vscode.window.showErrorMessage(`❌ Login failed: ${result.message}`);
            }
        });
    }
    async showUserProfile() {
        try {
            const user = await this.backendClient.getCurrentUser();
            const subscription = user.subscription;
            let message = `👤 ${user.name} (${user.email})\n\n`;
            if (subscription) {
                message += `💳 Plan: ${subscription.plan}\n`;
                message += `📊 Status: ${subscription.status}\n`;
                message += `🔢 Requests Remaining: ${subscription.api_requests_remaining}/${subscription.api_requests_limit}\n`;
                if (subscription.expires_at) {
                    message += `📅 Expires: ${new Date(subscription.expires_at).toLocaleDateString()}\n`;
                }
            }
            else {
                message += '⚠️ No subscription found';
            }
            const action = await vscode.window.showInformationMessage(message, 'Upgrade Plan', 'View Usage', 'Logout');
            if (action === 'Upgrade Plan') {
                vscode.env.openExternal(vscode.Uri.parse('https://goc-agent.com/pricing'));
            }
            else if (action === 'View Usage') {
                vscode.env.openExternal(vscode.Uri.parse('https://goc-agent.com/dashboard'));
            }
            else if (action === 'Logout') {
                await this.backendClient.logout();
                vscode.window.showInformationMessage('✅ Logged out successfully');
            }
        }
        catch (error) {
            vscode.window.showErrorMessage(`❌ Failed to load profile: ${error.message}`);
        }
    }
    async logout() {
        await this.backendClient.logout();
        vscode.window.showInformationMessage('✅ Logged out successfully');
    }
    async getModelsByProvider(provider) {
        const providerConfig = this.getProvider(provider);
        if (!providerConfig)
            return [];
        if (provider === 'ollama') {
            await this.refreshOllamaModels();
        }
        return providerConfig.models;
    }
    async checkOllamaStatus() {
        try {
            const response = await (0, node_fetch_1.default)('http://localhost:11434/api/tags', {
                method: 'GET',
                signal: AbortSignal.timeout(5000)
            });
            if (response.ok) {
                const data = await response.json();
                const models = data.models?.map((model) => model.name) || [];
                return {
                    isRunning: true,
                    hasModels: models.length > 0,
                    models
                };
            }
            else {
                return {
                    isRunning: false,
                    hasModels: false,
                    models: []
                };
            }
        }
        catch (error) {
            return {
                isRunning: false,
                hasModels: false,
                models: []
            };
        }
    }
    async showOllamaSetupDialog() {
        const action = await vscode.window.showInformationMessage('Ollama Setup Required', 'Ollama is not installed or not running. Would you like to install it?', 'Download Ollama', 'View Models', 'Cancel');
        if (action === 'Download Ollama') {
            vscode.env.openExternal(vscode.Uri.parse('https://ollama.ai/download'));
        }
        else if (action === 'View Models') {
            vscode.env.openExternal(vscode.Uri.parse('https://ollama.ai/library'));
        }
    }
    /**
     * Get detailed status information for display
     */
    getDetailedStatus() {
        const provider = this.getCurrentProvider();
        if (!provider) {
            return {
                provider: 'None',
                model: 'None',
                status: 'Not configured',
                description: 'No AI provider selected',
                requiresAuth: false,
                isAuthenticated: false
            };
        }
        if (provider.name === 'ollama') {
            return {
                provider: provider.displayName,
                model: this.formatOllamaModelName(this.configuration.model),
                status: 'Local',
                description: 'Free local AI models - No registration required',
                requiresAuth: false,
                isAuthenticated: true // Always "authenticated" for local models
            };
        }
        else if (provider.name === 'goc') {
            const isAuth = this.backendClient.isAuthenticated();
            const tierName = this.configuration.model === 'goc-agent-cloud' ? 'Free Tier' : 'Developer Tier';
            return {
                provider: provider.displayName,
                model: `${tierName}`,
                status: isAuth ? 'Authenticated' : 'Not authenticated',
                description: isAuth
                    ? `${tierName} - Hosted AI models`
                    : 'Registration required - 50 free requests/month, $29/month for 500 requests',
                requiresAuth: true,
                isAuthenticated: isAuth
            };
        }
        const isAuthenticated = provider.requiresApiKey ? !!this.configuration.apiKey : true;
        let status = 'Ready';
        let description = '';
        if (provider.name === 'ollama') {
            status = 'Local';
            description = 'Running locally on your machine';
        }
        else if (provider.name === 'goc') {
            if (!isAuthenticated) {
                status = 'Authentication required';
                description = 'Register at goc-agent.com to get API key';
            }
            else {
                status = 'Cloud';
                const tierName = this.configuration.model === 'goc-agent-cloud' ? 'Free' : 'Developer';
                description = `GOC Agent Model (${tierName} tier)`;
            }
        }
        return {
            provider: provider.displayName,
            model: this.formatModelName(this.configuration.model),
            status,
            description,
            requiresAuth: provider.requiresApiKey,
            isAuthenticated
        };
    }
    formatModelName(modelId) {
        if (!modelId)
            return 'None';
        // Handle Ollama models
        if (modelId.includes(':')) {
            return this.formatOllamaModelName(modelId);
        }
        // Handle GOC Agent models
        if (modelId === 'goc-agent-cloud')
            return 'Free Tier';
        if (modelId === 'goc-agent-dev')
            return 'Developer Tier';
        return modelId;
    }
}
exports.ConfigurationManager = ConfigurationManager;
ConfigurationManager.CONFIG_SECTION = 'gocAgent';
//# sourceMappingURL=configurationManager.js.map