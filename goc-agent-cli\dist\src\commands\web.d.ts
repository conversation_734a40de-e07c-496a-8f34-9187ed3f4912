/**
 * Web Research Command
 *
 * Advanced web research and content processing using GOC Core
 */
import { Command } from 'commander';
import { GocCore } from '@goc-agent/core';
export declare class WebCommand {
    private core;
    constructor(core: GocCore);
    register(program: Command): void;
    searchWeb(query: string, options: any): Promise<void>;
    private extractContent;
    researchTopic(topic: string, options: any): Promise<void>;
    private monitorTrends;
    private showInsights;
    private summarizeContent;
}
//# sourceMappingURL=web.d.ts.map