"use strict";
/**
 * Analyze Command
 *
 * Analyzes code files and projects
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyzeCommand = void 0;
const chalk_1 = __importDefault(require("chalk"));
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class AnalyzeCommand {
    constructor(core) {
        this.core = core;
    }
    register(program) {
        const analyzeCmd = program
            .command('analyze')
            .description('Analyze code files and projects');
        analyzeCmd
            .command('file <filepath>')
            .description('Analyze a specific file')
            .action(async (filepath) => {
            await this.analyzeFile(filepath);
        });
        analyzeCmd
            .command('project [directory]')
            .description('Analyze entire project')
            .action(async (directory = '.') => {
            await this.analyzeProject(directory);
        });
    }
    async analyzeFile(filepath) {
        try {
            if (!fs.existsSync(filepath)) {
                console.error(chalk_1.default.red('File not found:'), filepath);
                return;
            }
            const content = fs.readFileSync(filepath, 'utf-8');
            const stats = fs.statSync(filepath);
            console.log(chalk_1.default.blue('File Analysis:'), filepath);
            console.log(chalk_1.default.gray('Size:'), this.formatBytes(stats.size));
            console.log(chalk_1.default.gray('Lines:'), content.split('\n').length);
            console.log(chalk_1.default.gray('Extension:'), path.extname(filepath));
            // Basic analysis
            const analysis = this.performBasicAnalysis(content, filepath);
            console.log(chalk_1.default.green('Analysis Results:'));
            console.log(analysis);
        }
        catch (error) {
            console.error(chalk_1.default.red('Error analyzing file:'), error);
        }
    }
    async analyzeProject(directory, options) {
        try {
            if (!fs.existsSync(directory)) {
                console.error(chalk_1.default.red('Directory not found:'), directory);
                return;
            }
            console.log(chalk_1.default.blue('Project Analysis:'), directory);
            const files = this.getCodeFiles(directory);
            console.log(chalk_1.default.gray('Code files found:'), files.length);
            let totalLines = 0;
            let totalSize = 0;
            for (const file of files) {
                const content = fs.readFileSync(file, 'utf-8');
                const stats = fs.statSync(file);
                totalLines += content.split('\n').length;
                totalSize += stats.size;
            }
            console.log(chalk_1.default.green('Project Summary:'));
            console.log(chalk_1.default.gray('Total files:'), files.length);
            console.log(chalk_1.default.gray('Total lines:'), totalLines);
            console.log(chalk_1.default.gray('Total size:'), this.formatBytes(totalSize));
        }
        catch (error) {
            console.error(chalk_1.default.red('Error analyzing project:'), error);
        }
    }
    performBasicAnalysis(content, filepath) {
        const lines = content.split('\n');
        const extension = path.extname(filepath).toLowerCase();
        return {
            lineCount: lines.length,
            emptyLines: lines.filter(line => line.trim() === '').length,
            commentLines: this.countCommentLines(lines, extension),
            fileType: this.getFileType(extension),
            complexity: 'Basic' // Placeholder
        };
    }
    countCommentLines(lines, extension) {
        let count = 0;
        const commentPatterns = this.getCommentPatterns(extension);
        for (const line of lines) {
            const trimmed = line.trim();
            if (commentPatterns.some(pattern => trimmed.startsWith(pattern))) {
                count++;
            }
        }
        return count;
    }
    getCommentPatterns(extension) {
        switch (extension) {
            case '.js':
            case '.ts':
            case '.jsx':
            case '.tsx':
                return ['//', '/*'];
            case '.py':
                return ['#'];
            case '.php':
                return ['//', '/*', '#'];
            case '.html':
            case '.xml':
                return ['<!--'];
            default:
                return ['//'];
        }
    }
    getFileType(extension) {
        const types = {
            '.js': 'JavaScript',
            '.ts': 'TypeScript',
            '.jsx': 'React JSX',
            '.tsx': 'React TSX',
            '.py': 'Python',
            '.php': 'PHP',
            '.html': 'HTML',
            '.css': 'CSS',
            '.json': 'JSON'
        };
        return types[extension] || 'Unknown';
    }
    getCodeFiles(directory) {
        const files = [];
        const codeExtensions = ['.js', '.ts', '.jsx', '.tsx', '.py', '.php', '.html', '.css', '.json'];
        const scan = (dir) => {
            const items = fs.readdirSync(dir);
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stats = fs.statSync(fullPath);
                if (stats.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
                    scan(fullPath);
                }
                else if (stats.isFile() && codeExtensions.includes(path.extname(item))) {
                    files.push(fullPath);
                }
            }
        };
        scan(directory);
        return files;
    }
    formatBytes(bytes) {
        if (bytes === 0)
            return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}
exports.AnalyzeCommand = AnalyzeCommand;
//# sourceMappingURL=analyze.js.map