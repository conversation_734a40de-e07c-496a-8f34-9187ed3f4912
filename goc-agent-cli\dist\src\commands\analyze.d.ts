/**
 * Analyze Command
 *
 * Analyzes code files and projects
 */
import { Command } from 'commander';
import { GocCore } from '@goc-agent/core';
export declare class AnalyzeCommand {
    private core;
    constructor(core: GocCore);
    register(program: Command): void;
    private analyzeFile;
    analyzeProject(directory: string, options?: any): Promise<void>;
    private performBasicAnalysis;
    private countCommentLines;
    private getCommentPatterns;
    private getFileType;
    private getCodeFiles;
    private formatBytes;
}
//# sourceMappingURL=analyze.d.ts.map