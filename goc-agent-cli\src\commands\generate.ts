/**
 * Code Generation Command
 * 
 * Intelligent code generation with pattern recognition and best practices
 */

import chalk from 'chalk';
import inquirer from 'inquirer';
import { Command } from 'commander';
import { GocCore } from '@goc-agent/core';
import fs from 'fs';
import path from 'path';

export class GenerateCommand {
  constructor(private core: GocCore) {}

  register(program: Command): void {
    const generateCmd = program
      .command('generate')
      .alias('gen')
      .description('Intelligent code generation');

    generateCmd
      .command('code <description>')
      .description('Generate code from description')
      .option('-l, --language <lang>', 'Programming language', 'javascript')
      .option('-t, --type <type>', 'Code type (function|class|component|module|test)', 'function')
      .option('-o, --output <file>', 'Output file path')
      .option('--style <style>', 'Code style (modern|traditional|functional)', 'modern')
      .option('--tests', 'Include test generation')
      .option('--docs', 'Include documentation')
      .option('--patterns', 'Follow design patterns')
      .action(async (description, options) => {
        await this.generateCode(description, options);
      });

    generateCmd
      .command('refactor <file>')
      .description('Refactor existing code')
      .option('-l, --language <lang>', 'Programming language (auto-detect if not specified)')
      .option('--modernize', 'Apply modern patterns')
      .option('--preserve-logic', 'Preserve existing logic', true)
      .option('-o, --output <file>', 'Output file path (overwrites original if not specified)')
      .action(async (file, options) => {
        await this.refactorCode(file, options);
      });

    generateCmd
      .command('from-description <description>')
      .description('Generate code from natural language description')
      .option('-l, --language <lang>', 'Programming language', 'javascript')
      .option('-o, --output <file>', 'Output file path')
      .action(async (description, options) => {
        await this.generateFromDescription(description, options);
      });

    generateCmd
      .command('templates')
      .description('List available code templates')
      .option('-l, --language <lang>', 'Filter by programming language')
      .action(async (options) => {
        await this.listTemplates(options);
      });

    generateCmd
      .command('interactive')
      .alias('i')
      .description('Interactive code generation')
      .action(async () => {
        await this.interactiveGeneration();
      });
  }

  async generateCode(description: string, options: any): Promise<void> {
    try {
      console.log(chalk.blue(`🔧 Generating ${options.type} in ${options.language}`));
      console.log(chalk.dim(`Description: "${description}"`));
      console.log(chalk.dim('─'.repeat(60)));

      const codeGenerator = this.core.getCodeGenerator();
      
      const result = await codeGenerator.generateCode({
        type: options.type,
        language: options.language,
        description,
        context: {
          projectType: this.detectProjectType(),
          filePath: options.output
        },
        options: {
          style: options.style,
          includeTests: options.tests,
          includeDocumentation: options.docs,
          followPatterns: options.patterns
        }
      });

      console.log(chalk.blue('📝 Generated Code:'));
      console.log(chalk.dim('─'.repeat(40)));
      console.log(result.code);
      console.log(chalk.dim('─'.repeat(40)));
      console.log();

      console.log(chalk.blue('📖 Explanation:'));
      console.log(result.explanation);
      console.log();

      if (result.suggestions.length > 0) {
        console.log(chalk.blue('💡 Suggestions:'));
        result.suggestions.forEach((suggestion, index) => {
          console.log(`${index + 1}. ${suggestion}`);
        });
        console.log();
      }

      if (result.patterns.length > 0) {
        console.log(chalk.blue('🎨 Patterns Used:'));
        console.log(result.patterns.join(', '));
        console.log();
      }

      console.log(chalk.blue('📊 Quality Analysis:'));
      console.log(`Score: ${result.quality.score}/100`);
      if (result.quality.issues.length > 0) {
        console.log(chalk.yellow('Issues:'));
        result.quality.issues.forEach(issue => console.log(`  • ${issue}`));
      }
      if (result.quality.improvements.length > 0) {
        console.log(chalk.blue('Improvements:'));
        result.quality.improvements.forEach(improvement => console.log(`  • ${improvement}`));
      }
      console.log();

      if (result.tests && options.tests) {
        console.log(chalk.blue('🧪 Generated Tests:'));
        console.log(chalk.dim('─'.repeat(40)));
        console.log(result.tests);
        console.log(chalk.dim('─'.repeat(40)));
        console.log();
      }

      if (result.documentation && options.docs) {
        console.log(chalk.blue('📚 Generated Documentation:'));
        console.log(chalk.dim('─'.repeat(40)));
        console.log(result.documentation);
        console.log(chalk.dim('─'.repeat(40)));
        console.log();
      }

      // Save to file if specified
      if (options.output) {
        await this.saveGeneratedCode(options.output, result, options);
        console.log(chalk.green(`✅ Code saved to: ${options.output}`));
      } else {
        const { save } = await inquirer.prompt([{
          type: 'confirm',
          name: 'save',
          message: 'Save the generated code to a file?',
          default: true
        }]);

        if (save) {
          const { filename } = await inquirer.prompt([{
            type: 'input',
            name: 'filename',
            message: 'Enter filename:',
            default: `generated.${this.getFileExtension(options.language)}`
          }]);

          await this.saveGeneratedCode(filename, result, options);
          console.log(chalk.green(`✅ Code saved to: ${filename}`));
        }
      }

    } catch (error) {
      console.error(chalk.red('❌ Code generation failed:'), error);
    }
  }

  private async refactorCode(filePath: string, options: any): Promise<void> {
    try {
      console.log(chalk.blue(`🔄 Refactoring: ${filePath}`));
      console.log(chalk.dim('─'.repeat(60)));

      // Read existing code
      if (!fs.existsSync(filePath)) {
        console.error(chalk.red(`❌ File not found: ${filePath}`));
        return;
      }

      const code = fs.readFileSync(filePath, 'utf-8');
      const language = options.language || this.detectLanguage(filePath);

      console.log(chalk.blue('📊 Original Code Analysis:'));
      console.log(`Language: ${language}`);
      console.log(`Size: ${code.length} characters`);
      console.log(`Lines: ${code.split('\n').length}`);
      console.log();

      const codeGenerator = this.core.getCodeGenerator();
      
      const result = await codeGenerator.refactorCode(code, {
        language,
        preserveLogic: options.preserveLogic,
        modernize: options.modernize
      });

      console.log(chalk.blue('🔧 Refactored Code:'));
      console.log(chalk.dim('─'.repeat(40)));
      console.log(result.code);
      console.log(chalk.dim('─'.repeat(40)));
      console.log();

      console.log(chalk.blue('📖 Refactoring Summary:'));
      console.log(result.explanation);
      console.log();

      if (result.suggestions.length > 0) {
        console.log(chalk.blue('✨ Improvements Made:'));
        result.suggestions.forEach((suggestion, index) => {
          console.log(`${index + 1}. ${suggestion}`);
        });
        console.log();
      }

      console.log(chalk.blue('📊 Quality Comparison:'));
      console.log(`New Quality Score: ${result.quality.score}/100`);
      console.log();

      // Save refactored code
      const outputPath = options.output || filePath;
      const { confirm } = await inquirer.prompt([{
        type: 'confirm',
        name: 'confirm',
        message: `Save refactored code to ${outputPath}?`,
        default: true
      }]);

      if (confirm) {
        // Backup original if overwriting
        if (outputPath === filePath) {
          const backupPath = `${filePath}.backup.${Date.now()}`;
          fs.copyFileSync(filePath, backupPath);
          console.log(chalk.dim(`Original backed up to: ${backupPath}`));
        }

        fs.writeFileSync(outputPath, result.code, 'utf-8');
        console.log(chalk.green(`✅ Refactored code saved to: ${outputPath}`));
      }

    } catch (error) {
      console.error(chalk.red('❌ Code refactoring failed:'), error);
    }
  }

  private async generateFromDescription(description: string, options: any): Promise<void> {
    try {
      console.log(chalk.blue(`🤖 Generating code from natural language`));
      console.log(chalk.dim(`Description: "${description}"`));
      console.log(chalk.dim('─'.repeat(60)));

      const codeGenerator = this.core.getCodeGenerator();
      
      const result = await codeGenerator.generateFromDescription(
        description, 
        options.language,
        {
          projectPath: process.cwd(),
          projectType: this.detectProjectType()
        }
      );

      console.log(chalk.blue('🎯 Generated Code:'));
      console.log(chalk.dim('─'.repeat(40)));
      console.log(result.code);
      console.log(chalk.dim('─'.repeat(40)));
      console.log();

      console.log(chalk.blue('📖 Explanation:'));
      console.log(result.explanation);
      console.log();

      if (result.suggestions.length > 0) {
        console.log(chalk.blue('💡 Suggestions:'));
        result.suggestions.forEach((suggestion, index) => {
          console.log(`${index + 1}. ${suggestion}`);
        });
        console.log();
      }

      // Save to file if specified
      if (options.output) {
        fs.writeFileSync(options.output, result.code, 'utf-8');
        console.log(chalk.green(`✅ Code saved to: ${options.output}`));
      }

    } catch (error) {
      console.error(chalk.red('❌ Code generation from description failed:'), error);
    }
  }

  private async listTemplates(options: any): Promise<void> {
    try {
      console.log(chalk.blue('📋 Available Code Templates'));
      console.log(chalk.dim('─'.repeat(60)));

      const codeGenerator = this.core.getCodeGenerator();
      const templates = codeGenerator.getTemplates(options.language);

      if (templates.length === 0) {
        console.log(chalk.yellow('No templates available'));
        return;
      }

      // Group by language
      const groupedTemplates = templates.reduce((groups, template) => {
        const lang = template.language;
        if (!groups[lang]) groups[lang] = [];
        groups[lang].push(template);
        return groups;
      }, {} as Record<string, any[]>);

      Object.entries(groupedTemplates).forEach(([language, langTemplates]) => {
        console.log(chalk.bold(`\n${language.toUpperCase()}:`));
        langTemplates.forEach((template, index) => {
          console.log(`${index + 1}. ${chalk.bold(template.name)} (${template.category})`);
          console.log(chalk.dim(`   ${template.description}`));
          if (template.variables.length > 0) {
            const vars = template.variables.map((v: any) => v.name).join(', ');
            console.log(chalk.dim(`   Variables: ${vars}`));
          }
        });
      });

      console.log(chalk.dim('\nUse templates with the --type option in generate commands'));

    } catch (error) {
      console.error(chalk.red('❌ Failed to list templates:'), error);
    }
  }

  private async interactiveGeneration(): Promise<void> {
    try {
      console.log(chalk.blue('🎯 Interactive Code Generation'));
      console.log(chalk.dim('─'.repeat(60)));

      const answers = await inquirer.prompt([
        {
          type: 'input',
          name: 'description',
          message: 'Describe what you want to generate:',
          validate: (input) => input.trim().length > 0 || 'Description cannot be empty'
        },
        {
          type: 'list',
          name: 'language',
          message: 'Select programming language:',
          choices: ['javascript', 'typescript', 'python', 'php', 'java', 'go', 'rust']
        },
        {
          type: 'list',
          name: 'type',
          message: 'Select code type:',
          choices: ['function', 'class', 'component', 'module', 'test']
        },
        {
          type: 'list',
          name: 'style',
          message: 'Select code style:',
          choices: ['modern', 'traditional', 'functional']
        },
        {
          type: 'checkbox',
          name: 'features',
          message: 'Select additional features:',
          choices: [
            { name: 'Include tests', value: 'tests' },
            { name: 'Include documentation', value: 'docs' },
            { name: 'Follow design patterns', value: 'patterns' }
          ]
        },
        {
          type: 'input',
          name: 'output',
          message: 'Output file (optional):',
          default: ''
        }
      ]);

      const options = {
        language: answers.language,
        type: answers.type,
        style: answers.style,
        tests: answers.features.includes('tests'),
        docs: answers.features.includes('docs'),
        patterns: answers.features.includes('patterns'),
        output: answers.output || undefined
      };

      await this.generateCode(answers.description, options);

    } catch (error) {
      console.error(chalk.red('❌ Interactive generation failed:'), error);
    }
  }

  // Helper methods
  private async saveGeneratedCode(filePath: string, result: any, options: any): Promise<void> {
    // Ensure directory exists
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    let content = result.code;

    // Add documentation if generated
    if (result.documentation && options.docs) {
      content = result.documentation + '\n\n' + content;
    }

    fs.writeFileSync(filePath, content, 'utf-8');

    // Save tests separately if generated
    if (result.tests && options.tests) {
      const testPath = this.getTestFilePath(filePath, options.language);
      fs.writeFileSync(testPath, result.tests, 'utf-8');
      console.log(chalk.green(`✅ Tests saved to: ${testPath}`));
    }
  }

  private getFileExtension(language: string): string {
    const extensions: Record<string, string> = {
      javascript: 'js',
      typescript: 'ts',
      python: 'py',
      php: 'php',
      java: 'java',
      go: 'go',
      rust: 'rs',
      cpp: 'cpp',
      c: 'c'
    };
    return extensions[language] || 'txt';
  }

  private getTestFilePath(originalPath: string, language: string): string {
    const ext = path.extname(originalPath);
    const base = path.basename(originalPath, ext);
    const dir = path.dirname(originalPath);
    
    const testSuffix = language === 'python' ? '_test' : '.test';
    return path.join(dir, `${base}${testSuffix}${ext}`);
  }

  private detectLanguage(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    const languageMap: Record<string, string> = {
      '.js': 'javascript',
      '.ts': 'typescript',
      '.py': 'python',
      '.php': 'php',
      '.java': 'java',
      '.go': 'go',
      '.rs': 'rust',
      '.cpp': 'cpp',
      '.c': 'c'
    };
    return languageMap[ext] || 'javascript';
  }

  private detectProjectType(): string {
    // Simple project type detection based on files in current directory
    const files = fs.readdirSync(process.cwd());
    
    if (files.includes('package.json')) return 'nodejs';
    if (files.includes('composer.json')) return 'php';
    if (files.includes('requirements.txt') || files.includes('setup.py')) return 'python';
    if (files.includes('pom.xml') || files.includes('build.gradle')) return 'java';
    if (files.includes('Cargo.toml')) return 'rust';
    if (files.includes('go.mod')) return 'go';
    
    return 'general';
  }

  async generateTests(filePath: string, options: any): Promise<void> {
    try {
      console.log(chalk.blue(`🧪 Generating tests for: ${filePath}`));
      console.log(chalk.dim(`Framework: ${options.framework || 'auto-detect'}`));
      console.log(chalk.dim('─'.repeat(60)));

      const fs = require('fs');
      if (!fs.existsSync(filePath)) {
        console.error(chalk.red('❌ File not found:'), filePath);
        return;
      }

      const content = fs.readFileSync(filePath, 'utf8');
      const codeGenerator = this.core.getCodeGenerator();

      const result = await codeGenerator.generateCode({
        type: 'test',
        language: 'javascript',
        description: `Generate comprehensive unit tests for the code in ${filePath}. Include test cases for all functions, edge cases, and error handling.`,
        context: {
          existingCode: content,
          filePath: filePath
        }
      });

      if (result.code) {
        console.log(chalk.green('✅ Tests generated successfully!'));
        console.log(chalk.dim('─'.repeat(50)));

        const testFilePath = filePath.replace(/\.(ts|js|py|php)$/, '.test.$1');
        console.log(`Test file: ${testFilePath}`);

        console.log(chalk.blue('\n📝 Generated Test Code:'));
        console.log(chalk.dim('─'.repeat(40)));
        console.log(result.code.substring(0, 500) + (result.code.length > 500 ? '...' : ''));

        if (options.save !== false) {
          fs.writeFileSync(testFilePath, result.code);
          console.log(chalk.green(`\n💾 Tests saved to: ${testFilePath}`));
        }

        if (result.explanation) {
          console.log(chalk.blue('\n💡 Explanation:'));
          console.log(result.explanation);
        }
      } else {
        console.error(chalk.red('❌ Test generation failed: No code generated'));
      }

    } catch (error) {
      console.error(chalk.red('❌ Test generation error:'), error);
    }
  }
}
