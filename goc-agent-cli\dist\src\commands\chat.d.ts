/**
 * Chat Command
 *
 * Interactive chat with AI using GOC Core
 */
import { Command } from 'commander';
import { <PERSON><PERSON><PERSON><PERSON>, ConfigManager } from '@goc-agent/core';
import { BackendAPIClient } from '../../services/BackendAPIClient';
export declare class ChatCommand {
    private core;
    private configManager;
    private apiClient?;
    constructor(core: Goc<PERSON>ore, configManager: ConfigManager, apiClient?: BackendAPIClient | undefined);
    register(program: Command): void;
    execute(options: any): Promise<void>;
    private shouldUseBackend;
    private getAIResponse;
    startInteractiveChat(options: any): Promise<void>;
}
//# sourceMappingURL=chat.d.ts.map