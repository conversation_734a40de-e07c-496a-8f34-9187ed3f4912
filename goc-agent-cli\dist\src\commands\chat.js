"use strict";
/**
 * Chat Command
 *
 * Interactive chat with AI using GOC Core
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatCommand = void 0;
const chalk_1 = __importDefault(require("chalk"));
const inquirer_1 = __importDefault(require("inquirer"));
const core_1 = require("@goc-agent/core");
class ChatCommand {
    constructor(core, configManager, apiClient) {
        this.core = core;
        this.configManager = configManager;
        this.apiClient = apiClient;
    }
    register(program) {
        program
            .command('chat')
            .alias('c')
            .description('Start interactive chat with AI')
            .option('-p, --provider <provider>', 'AI provider to use')
            .option('-m, --model <model>', 'AI model to use')
            .option('-s, --session <sessionId>', 'Use existing session')
            .option('--backend', 'Force use backend API')
            .option('--local', 'Force use local AI providers')
            .action(async (options) => {
            await this.execute(options);
        });
    }
    async execute(options) {
        console.log(chalk_1.default.blue('🤖 GOC Agent Chat'));
        console.log(chalk_1.default.gray('Type "exit" to quit, "clear" to clear history\n'));
        const config = this.configManager.getConfig();
        const provider = options.provider || config.ai.defaultProvider;
        const model = options.model || config.ai.defaultModel;
        // Determine if we should use backend or local mode
        const useBackend = this.shouldUseBackend(options);
        let sessionId = options.session || null;
        if (useBackend) {
            console.log(chalk_1.default.dim(`Mode: Backend API`));
            if (sessionId) {
                console.log(chalk_1.default.dim(`Session: ${sessionId}`));
            }
            else {
                // Create a new session
                try {
                    const session = await this.apiClient.createSession(`Chat ${new Date().toLocaleString()}`);
                    sessionId = session.id;
                    console.log(chalk_1.default.dim(`Created session: ${sessionId}`));
                }
                catch (error) {
                    console.log(chalk_1.default.yellow('⚠️ Failed to create backend session, falling back to local mode'));
                    console.log(chalk_1.default.dim(`Using: ${provider} - ${model}\n`));
                }
            }
        }
        else {
            console.log(chalk_1.default.dim(`Mode: Local AI`));
            console.log(chalk_1.default.dim(`Using: ${provider} - ${model}\n`));
        }
        const messages = [];
        while (true) {
            try {
                // Get user input
                const { message } = await inquirer_1.default.prompt([
                    {
                        type: 'input',
                        name: 'message',
                        message: chalk_1.default.green('You:'),
                        validate: (input) => input.trim().length > 0 || 'Please enter a message'
                    }
                ]);
                const userMessage = message.trim();
                // Handle special commands
                if (userMessage.toLowerCase() === 'exit') {
                    console.log(chalk_1.default.yellow('Goodbye! 👋'));
                    break;
                }
                if (userMessage.toLowerCase() === 'clear') {
                    messages.length = 0;
                    console.clear();
                    console.log(chalk_1.default.blue('🤖 GOC Agent Chat'));
                    console.log(chalk_1.default.gray('Chat history cleared\n'));
                    continue;
                }
                // Add user message to history
                messages.push({
                    role: 'user',
                    content: userMessage,
                    timestamp: new Date()
                });
                // Show thinking indicator
                process.stdout.write(chalk_1.default.dim('🤔 Thinking...'));
                try {
                    let response;
                    if (useBackend && sessionId && this.apiClient) {
                        // Use backend API
                        const chatResponse = await this.apiClient.chat({
                            session_id: sessionId,
                            message: userMessage,
                            provider,
                            model
                        });
                        response = { content: chatResponse.assistant_message.content };
                    }
                    else {
                        // Use local AI providers
                        response = await this.getAIResponse(messages, { model, provider });
                    }
                    // Clear thinking indicator
                    process.stdout.write('\r' + ' '.repeat(20) + '\r');
                    // Add AI response to history
                    messages.push({
                        role: 'assistant',
                        content: response.content,
                        timestamp: new Date()
                    });
                    // Display response
                    console.log(chalk_1.default.cyan('GOC Agent:'), response.content);
                    console.log(); // Empty line for spacing
                }
                catch (error) {
                    process.stdout.write('\r' + ' '.repeat(20) + '\r');
                    console.error(chalk_1.default.red('Error getting AI response:'), error);
                    core_1.logger.error('Chat AI response failed', error);
                }
            }
            catch (error) {
                if (error instanceof Error && error.name === 'ExitPromptError') {
                    // User pressed Ctrl+C
                    console.log(chalk_1.default.yellow('\nGoodbye! 👋'));
                    break;
                }
                console.error(chalk_1.default.red('Chat error:'), error);
                core_1.logger.error('Chat command failed', error);
            }
        }
    }
    shouldUseBackend(options) {
        // Force backend mode if specified
        if (options.backend)
            return true;
        // Force local mode if specified
        if (options.local)
            return false;
        // Use backend if authenticated and available
        if (this.apiClient?.isAuthenticated()) {
            return true;
        }
        // Default to local mode
        return false;
    }
    async getAIResponse(messages, options) {
        // This is a placeholder implementation
        // In the real implementation, this would use the AI providers from goc-core
        // Simulate AI processing delay
        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
        const lastMessage = messages[messages.length - 1];
        // Simple response generation for demo
        const responses = [
            "I understand you're asking about: " + lastMessage.content,
            "That's an interesting question. Let me help you with that.",
            "Based on your request, here's what I can suggest...",
            "I can help you with that. Here's my analysis:",
            "Great question! Let me break this down for you."
        ];
        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
        return {
            content: randomResponse + "\n\n(Note: This is a demo response. Full AI integration coming soon!)"
        };
    }
    // Public method for CLI integration
    async startInteractiveChat(options) {
        await this.execute(options);
    }
}
exports.ChatCommand = ChatCommand;
//# sourceMappingURL=chat.js.map