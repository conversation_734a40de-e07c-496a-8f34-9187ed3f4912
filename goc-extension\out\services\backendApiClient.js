"use strict";
/**
 * Backend API Client for VS Code Extension
 *
 * Handles authentication and API requests to GOC Agent backend
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BackendAPIClient = void 0;
const node_fetch_1 = __importDefault(require("node-fetch"));
class BackendAPIClient {
    constructor(context) {
        this.token = null;
        this.context = context;
        this.baseUrl = 'https://api.goc-agent.com'; // Production URL
        // this.baseUrl = 'http://localhost:8000'; // Development URL
        // Load saved token
        this.token = this.context.globalState.get('goc_auth_token', null);
    }
    async makeRequest(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'GOC-Agent-Extension/1.0.0',
        };
        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }
        const response = await (0, node_fetch_1.default)(url, {
            ...options,
            headers: {
                ...headers,
                ...options.headers,
            },
        });
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
        }
        return await response.json();
    }
    isAuthenticated() {
        return !!this.token;
    }
    async register(name, email, password) {
        try {
            const response = await this.makeRequest('/auth/register', {
                method: 'POST',
                body: JSON.stringify({
                    name,
                    email,
                    password,
                    password_confirmation: password,
                }),
            });
            if (response.success && response.token) {
                await this.saveToken(response.token);
            }
            return {
                success: response.success,
                token: response.token,
                user: response.user,
                message: response.message,
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || 'Registration failed',
            };
        }
    }
    async login(email, password) {
        try {
            const response = await this.makeRequest('/auth/login', {
                method: 'POST',
                body: JSON.stringify({
                    email,
                    password,
                }),
            });
            if (response.success && response.token) {
                await this.saveToken(response.token);
            }
            return {
                success: response.success,
                token: response.token,
                user: response.user,
                message: response.message,
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || 'Login failed',
            };
        }
    }
    async logout() {
        try {
            if (this.isAuthenticated()) {
                await this.makeRequest('/auth/logout', {
                    method: 'POST',
                });
            }
        }
        catch (error) {
            // Ignore logout errors
        }
        finally {
            await this.clearToken();
        }
    }
    async getCurrentUser() {
        const response = await this.makeRequest('/auth/user');
        return response.user;
    }
    async chat(request) {
        const response = await this.makeRequest('/agent/chat', {
            method: 'POST',
            body: JSON.stringify(request),
        });
        return response;
    }
    async executeTask(task, sessionId, provider, model, autoMode) {
        const response = await this.makeRequest('/agent/task', {
            method: 'POST',
            body: JSON.stringify({
                task,
                session_id: sessionId,
                provider,
                model,
                auto_mode: autoMode,
            }),
        });
        return response;
    }
    async getProviders() {
        const response = await this.makeRequest('/agent/providers');
        return response;
    }
    async getStatus() {
        try {
            const response = await this.makeRequest('/agent/status');
            return response;
        }
        catch (error) {
            return { status: 'error', message: 'Backend not available' };
        }
    }
    async healthCheck() {
        try {
            const response = await this.makeRequest('/health');
            return response.status === 'ok';
        }
        catch (error) {
            return false;
        }
    }
    async saveToken(token) {
        this.token = token;
        await this.context.globalState.update('goc_auth_token', token);
    }
    async clearToken() {
        this.token = null;
        await this.context.globalState.update('goc_auth_token', null);
    }
    /**
     * Check if user has access to GOC Agent models
     */
    async validateGocAgentAccess() {
        try {
            if (!this.isAuthenticated()) {
                return {
                    hasAccess: false,
                    plan: 'none',
                    requestsRemaining: 0,
                    message: 'Authentication required'
                };
            }
            const user = await this.getCurrentUser();
            const subscription = user.subscription;
            if (!subscription) {
                return {
                    hasAccess: false,
                    plan: 'none',
                    requestsRemaining: 0,
                    message: 'No subscription found'
                };
            }
            return {
                hasAccess: subscription.api_requests_remaining > 0,
                plan: subscription.plan,
                requestsRemaining: subscription.api_requests_remaining,
                message: subscription.api_requests_remaining === 0 ? 'Request limit exceeded' : undefined
            };
        }
        catch (error) {
            return {
                hasAccess: false,
                plan: 'error',
                requestsRemaining: 0,
                message: error.message || 'Failed to validate access'
            };
        }
    }
}
exports.BackendAPIClient = BackendAPIClient;
//# sourceMappingURL=backendApiClient.js.map