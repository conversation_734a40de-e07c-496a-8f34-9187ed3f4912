/**
 * Environment Configuration Loader
 * 
 * Loads configuration from environment variables, .env files, and command line arguments
 */

import fs from 'fs';
import path from 'path';
import chalk from 'chalk';

export interface EnvironmentConfig {
  // API Keys
  GOC_API_KEY?: string;
  OPENAI_API_KEY?: string;
  ANTHROPIC_API_KEY?: string;
  GOOGLE_API_KEY?: string;
  GROQ_API_KEY?: string;

  // Provider URLs
  OLLAMA_BASE_URL?: string;
  GOC_BASE_URL?: string;

  // Feature Flags
  GOC_WEB_RESEARCH?: string;
  GOC_AUTO_TRAINING?: string;
  GOC_TELEMETRY?: string;
  GOC_DEBUG?: string;

  // Performance
  GOC_MAX_MEMORY?: string;
  GOC_CACHE_SIZE?: string;
  GOC_REQUEST_TIMEOUT?: string;
  GOC_CONCURRENT_REQUESTS?: string;

  // Environment
  NODE_ENV?: string;
  GOC_ENV?: string;
  GOC_LOG_LEVEL?: string;

  // Security
  GOC_ENCRYPT_KEYS?: string;
  GOC_VALIDATE_CERTS?: string;
}

export class EnvironmentLoader {
  private envConfig: EnvironmentConfig = {};
  private envFilePaths: string[] = [];

  constructor() {
    this.envFilePaths = [
      '.env',
      '.env.local',
      '.env.production',
      path.join(process.cwd(), '.env'),
      path.join(process.cwd(), '.env.local'),
      path.join(process.cwd(), '.env.production')
    ];
  }

  async load(): Promise<EnvironmentConfig> {
    // Load from .env files
    await this.loadEnvFiles();

    // Load from environment variables
    this.loadEnvironmentVariables();

    // Load from command line arguments
    this.loadCommandLineArgs();

    return this.envConfig;
  }

  private async loadEnvFiles(): Promise<void> {
    for (const envPath of this.envFilePaths) {
      if (fs.existsSync(envPath)) {
        try {
          const envContent = fs.readFileSync(envPath, 'utf8');
          this.parseEnvContent(envContent);
          console.log(chalk.dim(`Loaded environment from: ${envPath}`));
        } catch (error) {
          console.warn(chalk.yellow(`Failed to load ${envPath}:`), error);
        }
      }
    }
  }

  private parseEnvContent(content: string): void {
    const lines = content.split('\n');
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // Skip comments and empty lines
      if (!trimmedLine || trimmedLine.startsWith('#')) {
        continue;
      }

      // Parse key=value pairs
      const equalIndex = trimmedLine.indexOf('=');
      if (equalIndex === -1) {
        continue;
      }

      const key = trimmedLine.substring(0, equalIndex).trim();
      let value = trimmedLine.substring(equalIndex + 1).trim();

      // Remove quotes if present
      if ((value.startsWith('"') && value.endsWith('"')) ||
          (value.startsWith("'") && value.endsWith("'"))) {
        value = value.slice(1, -1);
      }

      // Only set if it's a GOC-related variable or known API key
      if (this.isRelevantEnvVar(key)) {
        (this.envConfig as any)[key] = value;
      }
    }
  }

  private loadEnvironmentVariables(): void {
    const relevantVars = [
      'GOC_API_KEY', 'OPENAI_API_KEY', 'ANTHROPIC_API_KEY', 'GOOGLE_API_KEY', 'GROQ_API_KEY',
      'OLLAMA_BASE_URL', 'GOC_BASE_URL',
      'GOC_WEB_RESEARCH', 'GOC_AUTO_TRAINING', 'GOC_TELEMETRY', 'GOC_DEBUG',
      'GOC_MAX_MEMORY', 'GOC_CACHE_SIZE', 'GOC_REQUEST_TIMEOUT', 'GOC_CONCURRENT_REQUESTS',
      'NODE_ENV', 'GOC_ENV', 'GOC_LOG_LEVEL',
      'GOC_ENCRYPT_KEYS', 'GOC_VALIDATE_CERTS'
    ];

    for (const varName of relevantVars) {
      const value = process.env[varName];
      if (value !== undefined) {
        (this.envConfig as any)[varName] = value;
      }
    }
  }

  private loadCommandLineArgs(): void {
    const args = process.argv.slice(2);
    
    for (let i = 0; i < args.length; i++) {
      const arg = args[i];
      
      // Handle --key=value format
      if (arg.includes('=')) {
        const [key, value] = arg.split('=', 2);
        this.processCommandLineArg(key, value);
      }
      // Handle --key value format
      else if (arg.startsWith('--') && i + 1 < args.length) {
        const key = arg;
        const value = args[i + 1];
        if (!value.startsWith('-')) {
          this.processCommandLineArg(key, value);
          i++; // Skip the value in next iteration
        }
      }
    }
  }

  private processCommandLineArg(key: string, value: string): void {
    // Convert command line args to environment variable format
    const envVarMappings: { [key: string]: string } = {
      '--api-key': 'GOC_API_KEY',
      '--goc-api-key': 'GOC_API_KEY',
      '--openai-key': 'OPENAI_API_KEY',
      '--anthropic-key': 'ANTHROPIC_API_KEY',
      '--google-key': 'GOOGLE_API_KEY',
      '--groq-key': 'GROQ_API_KEY',
      '--ollama-url': 'OLLAMA_BASE_URL',
      '--goc-url': 'GOC_BASE_URL',
      '--web-research': 'GOC_WEB_RESEARCH',
      '--auto-training': 'GOC_AUTO_TRAINING',
      '--telemetry': 'GOC_TELEMETRY',
      '--debug': 'GOC_DEBUG',
      '--max-memory': 'GOC_MAX_MEMORY',
      '--cache-size': 'GOC_CACHE_SIZE',
      '--timeout': 'GOC_REQUEST_TIMEOUT',
      '--concurrent': 'GOC_CONCURRENT_REQUESTS',
      '--env': 'GOC_ENV',
      '--log-level': 'GOC_LOG_LEVEL',
      '--encrypt-keys': 'GOC_ENCRYPT_KEYS',
      '--validate-certs': 'GOC_VALIDATE_CERTS'
    };

    const envVar = envVarMappings[key];
    if (envVar) {
      (this.envConfig as any)[envVar] = value;
    }
  }

  private isRelevantEnvVar(key: string): boolean {
    const relevantPrefixes = ['GOC_', 'OPENAI_', 'ANTHROPIC_', 'GOOGLE_', 'GROQ_', 'OLLAMA_'];
    const relevantVars = ['NODE_ENV'];
    
    return relevantPrefixes.some(prefix => key.startsWith(prefix)) || 
           relevantVars.includes(key);
  }

  // Utility methods for type conversion
  getBooleanValue(key: keyof EnvironmentConfig, defaultValue: boolean = false): boolean {
    const value = this.envConfig[key];
    if (value === undefined) return defaultValue;
    
    const lowerValue = value.toLowerCase();
    return lowerValue === 'true' || lowerValue === '1' || lowerValue === 'yes';
  }

  getNumberValue(key: keyof EnvironmentConfig, defaultValue: number = 0): number {
    const value = this.envConfig[key];
    if (value === undefined) return defaultValue;
    
    const numValue = parseInt(value, 10);
    return isNaN(numValue) ? defaultValue : numValue;
  }

  getStringValue(key: keyof EnvironmentConfig, defaultValue: string = ''): string {
    return this.envConfig[key] || defaultValue;
  }

  // Validation methods
  validateApiKeys(): { valid: boolean; missing: string[] } {
    const missing: string[] = [];
    
    // Check for at least one AI provider API key
    const hasOllama = this.envConfig.OLLAMA_BASE_URL || 'http://localhost:11434';
    const hasGoc = this.envConfig.GOC_API_KEY;
    const hasOpenAI = this.envConfig.OPENAI_API_KEY;
    const hasAnthropic = this.envConfig.ANTHROPIC_API_KEY;
    const hasGoogle = this.envConfig.GOOGLE_API_KEY;
    const hasGroq = this.envConfig.GROQ_API_KEY;

    if (!hasOllama && !hasGoc && !hasOpenAI && !hasAnthropic && !hasGoogle && !hasGroq) {
      missing.push('At least one AI provider API key or Ollama URL');
    }

    return {
      valid: missing.length === 0,
      missing
    };
  }

  // Security check
  checkSensitiveDataExposure(): string[] {
    const warnings: string[] = [];
    
    // Check if API keys are in command line args (visible in process list)
    const args = process.argv.join(' ');
    const sensitivePatterns = [
      /--api-key[=\s]+[a-zA-Z0-9-_]+/,
      /--.*-key[=\s]+[a-zA-Z0-9-_]+/,
      /GOC_API_KEY[=\s]+[a-zA-Z0-9-_]+/
    ];

    for (const pattern of sensitivePatterns) {
      if (pattern.test(args)) {
        warnings.push('API keys detected in command line arguments (security risk)');
        break;
      }
    }

    return warnings;
  }

  // Generate example .env file
  generateExampleEnv(): string {
    return `# GOC Agent Environment Configuration
# Copy this file to .env and fill in your API keys

# AI Provider API Keys (at least one required)
# GOC_API_KEY=your_goc_api_key_here
# OPENAI_API_KEY=your_openai_api_key_here
# ANTHROPIC_API_KEY=your_anthropic_api_key_here
# GOOGLE_API_KEY=your_google_api_key_here
# GROQ_API_KEY=your_groq_api_key_here

# Provider URLs
OLLAMA_BASE_URL=http://localhost:11434
# GOC_BASE_URL=https://api.goc-agent.com

# Feature Flags
GOC_WEB_RESEARCH=true
GOC_AUTO_TRAINING=true
GOC_TELEMETRY=false
# GOC_DEBUG=false

# Performance Settings
# GOC_MAX_MEMORY=1024
# GOC_CACHE_SIZE=100
# GOC_REQUEST_TIMEOUT=30000
# GOC_CONCURRENT_REQUESTS=3

# Environment
# NODE_ENV=production
# GOC_ENV=production
# GOC_LOG_LEVEL=info

# Security
# GOC_ENCRYPT_KEYS=true
# GOC_VALIDATE_CERTS=true
`;
  }
}
