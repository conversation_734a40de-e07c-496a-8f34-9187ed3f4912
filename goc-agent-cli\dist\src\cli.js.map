{"version": 3, "file": "cli.js", "sourceRoot": "", "sources": ["../../src/cli.ts"], "names": [], "mappings": ";;AAEA;;;;GAIG;;;;;AAEH,yCAAoC;AACpC,kDAA0B;AAC1B,kDAA0B;AAC1B,8CAAsB;AACtB,0CAAgF;AAChF,0CAA8C;AAC9C,4CAAgD;AAChD,8CAAkD;AAClD,gDAAoD;AACpD,4CAAgD;AAChD,kDAAsD;AACtD,0CAA8C;AAC9C,gDAAoD;AACpD,4CAAgD;AAChD,gDAAoD;AACpD,wCAA4C;AAC5C,0CAA8C;AAC9C,kDAAsD;AACtD,gDAAoD;AACpD,0CAA8C;AAC9C,8CAAkD;AAClD,8CAAkD;AAClD,mEAAgE;AAEhE,MAAM,MAAM;IAMV;QAJQ,SAAI,GAAmB,IAAI,CAAC;QAC5B,kBAAa,GAAyB,IAAI,CAAC;QAIjD,IAAI,CAAC,OAAO,GAAG,IAAI,mBAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,SAAS,GAAG,IAAI,mCAAgB,EAAE,CAAC;QACxC,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAEO,YAAY;QAClB,IAAI,CAAC,OAAO;aACT,IAAI,CAAC,KAAK,CAAC;aACX,WAAW,CAAC,yCAAyC,CAAC;aACtD,OAAO,CAAC,OAAO,CAAC;aAChB,MAAM,CAAC,eAAe,EAAE,uBAAuB,CAAC;aAChD,MAAM,CAAC,aAAa,EAAE,+BAA+B,CAAC;aACtD,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE,EAAE;YACvC,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;YAC1C,yCAAyC;YACzC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,gBAAgB;QACtB,gDAAgD;QAChD,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtC,OAAO;QACT,CAAC;QAED,kCAAkC;QAClC,MAAM,WAAW,GAAG,IAAI,kBAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpD,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE3C,sCAAsC;QACtC,MAAM,cAAc,GAAG,IAAI,wBAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1D,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEtC,yBAAyB;QACzB,MAAM,YAAY,GAAG,IAAI,oBAAY,EAAE,CAAC;QACxC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEpC,2BAA2B;QAC3B,MAAM,cAAc,GAAG,IAAI,wBAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEtC,uBAAuB;QACvB,MAAM,UAAU,GAAG,IAAI,gBAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAElC,6BAA6B;QAC7B,MAAM,WAAW,GAAG,IAAI,kBAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEnC,mCAAmC;QACnC,MAAM,eAAe,GAAG,IAAI,0BAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEvC,8BAA8B;QAC9B,MAAM,cAAc,GAAG,IAAI,wBAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEtC,iCAAiC;QACjC,MAAM,WAAW,GAAG,IAAI,kBAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEnC,8BAA8B;QAC9B,MAAM,aAAa,GAAG,IAAI,sBAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAErC,0BAA0B;QAC1B,MAAM,WAAW,GAAG,IAAI,kBAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACnF,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnC,MAAM,YAAY,GAAG,IAAI,oBAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QACrE,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,MAAM,aAAa,GAAG,IAAI,sBAAa,EAAE,CAAC;QAC1C,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAErC,MAAM,cAAc,GAAG,IAAI,wBAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEtC,MAAM,YAAY,GAAG,IAAI,oBAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjD,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEpC,MAAM,eAAe,GAAG,IAAI,0BAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEvC,0BAA0B;QAC1B,MAAM,aAAa,GAAG,IAAI,sBAAa,EAAE,CAAC;QAC1C,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAErC,iBAAiB;QACjB,IAAI,CAAC,OAAO;aACT,OAAO,CAAC,QAAQ,CAAC;aACjB,WAAW,CAAC,uBAAuB,CAAC;aACpC,MAAM,CAAC,KAAK,IAAI,EAAE;YACjB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,OAAY;QACnC,IAAI,CAAC;YACH,mBAAmB;YACnB,aAAM,CAAC,YAAY,CAAC;gBAClB,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM;gBACnE,aAAa,EAAE,IAAI;aACpB,CAAC,CAAC;YAEH,8BAA8B;YAC9B,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;gBACnB,MAAM,OAAO,GAAG,IAAA,aAAG,EAAC,2BAA2B,CAAC,CAAC,KAAK,EAAE,CAAC;gBAEzD,IAAI,CAAC;oBACH,2BAA2B;oBAC3B,IAAI,CAAC,aAAa,GAAG,IAAI,oBAAa,EAAE,CAAC;oBACzC,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;oBAEtC,kBAAkB;oBAClB,IAAI,CAAC,IAAI,GAAG,MAAM,IAAA,oBAAa,EAAC;wBAC9B,EAAE,EAAE;4BACF,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,eAAe;4BAClE,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,YAAY;yBAC7D;wBACD,MAAM,EAAE;4BACN,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM;yBAC7C;qBACF,CAAC,CAAC;oBAEH,OAAO,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;gBACxD,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;oBAC/C,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,wBAAwB;gBACxB,IAAI,CAAC,aAAa,GAAG,IAAI,oBAAa,EAAE,CAAC;gBACzC,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;gBAEtC,IAAI,CAAC,IAAI,GAAG,MAAM,IAAA,oBAAa,EAAC;oBAC9B,EAAE,EAAE;wBACF,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,eAAe;wBAClE,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,YAAY;qBAC7D;iBACF,CAAC,CAAC;YACL,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,iCAAiC,CAAC,EAAE,KAAK,CAAC,CAAC;YACnE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU;QACtB,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtC,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC,CAAC;YACtD,OAAO;QACT,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;QAC9C,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;QAEhD,6BAA6B;QAC7B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;QAC1D,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;QAEzD,MAAM,UAAU,GAAG;YACjB,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC,eAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,eAAK,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YACzE,CAAC,SAAS,EAAE,OAAO,CAAC;YACpB,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE,CAAC,eAAe,CAAC;YACvC,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,CAAC,YAAY,CAAC;YACjC,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC,CAAC,eAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAChF,CAAC,gBAAgB,EAAE,eAAe,CAAC,CAAC,CAAC,eAAK,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,eAAK,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;YACnG,CAAC,aAAa,EAAE,0BAA0B,CAAC;SAC5C,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,IAAA,eAAK,EACf,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,eAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAC3E;YACE,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE,CAAC;YACV,WAAW,EAAE,OAAO;YACpB,WAAW,EAAE,MAAM;SACpB,CACF,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,+DAA+D,CAAC,CAAC,CAAC;YAC3F,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,sEAAsE,CAAC,CAAC,CAAC;QACjG,CAAC;QAED,IAAI,CAAC,eAAe,IAAI,cAAc,EAAE,CAAC;YACvC,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,2EAA2E,CAAC,CAAC,CAAC;QACvG,CAAC;IACH,CAAC;IAED,KAAK,CAAC,GAAG;QACP,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,aAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAc,CAAC,CAAC;YACrD,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,CAAC;YAC1C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;gBAAS,CAAC;YACT,UAAU;YACV,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;gBACd,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAC5B,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAED,6BAA6B;AAC7B,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,KAAK,EAAE,EAAE;IACxC,aAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;IAC1C,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,qBAAqB,CAAC,EAAE,KAAK,CAAC,CAAC;IACvD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC;AAEH,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;IACnD,aAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,MAAe,CAAC,CAAC;IACrD,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,MAAM,CAAC,CAAC;IACzD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC;AAEH,UAAU;AACV,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,MAAM,GAAG,GAAG,IAAI,MAAM,EAAE,CAAC;IACzB,GAAG,CAAC,GAAG,EAAE,CAAC;AACZ,CAAC"}