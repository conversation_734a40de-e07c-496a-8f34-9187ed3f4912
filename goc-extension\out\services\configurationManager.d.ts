import * as vscode from 'vscode';
export interface GocConfiguration {
    provider: string;
    model: string;
    apiKey: string;
    autoTraining: boolean;
    webResearch: boolean;
    contextLines: number;
    showProgress: boolean;
    autoSave: boolean;
    autoAnalysis: boolean;
}
export interface ProviderConfig {
    name: string;
    displayName: string;
    models: string[];
    requiresApiKey: boolean;
    defaultModel: string;
}
export declare class ConfigurationManager {
    private static readonly CONFIG_SECTION;
    private configuration;
    private backendClient;
    private providers;
    constructor(context: vscode.ExtensionContext);
    getConfiguration(): GocConfiguration;
    getProviders(): Promise<ProviderConfig[]>;
    refreshOllamaModels(): Promise<void>;
    private fetchOllamaModels;
    getProvider(name: string): ProviderConfig | undefined;
    getCurrentProvider(): ProviderConfig | undefined;
    setProvider(providerName: string): Promise<void>;
    setModel(modelName: string): Promise<void>;
    setApiKey(apiKey: string): Promise<void>;
    updateSetting<K extends keyof GocConfiguration>(key: K, value: GocConfiguration[K]): Promise<void>;
    reloadConfiguration(): void;
    private loadConfiguration;
    validateConfiguration(): {
        isValid: boolean;
        errors: string[];
    };
    promptForApiKey(providerName?: string): Promise<string | undefined>;
    selectProvider(): Promise<string | undefined>;
    selectModel(): Promise<string | undefined>;
    private selectOllamaModel;
    private selectGocAgentModel;
    getStatusText(): string;
    private formatOllamaModelName;
    private getOllamaModelDescription;
    private getOllamaModelSize;
    private handleGocAgentAuth;
    showRegistrationDialog(): Promise<void>;
    showLoginDialog(): Promise<void>;
    showUserProfile(): Promise<void>;
    logout(): Promise<void>;
    getModelsByProvider(provider: string): Promise<string[]>;
    checkOllamaStatus(): Promise<{
        isRunning: boolean;
        hasModels: boolean;
        models: string[];
    }>;
    showOllamaSetupDialog(): Promise<void>;
    /**
     * Get detailed status information for display
     */
    getDetailedStatus(): {
        provider: string;
        model: string;
        status: string;
        description: string;
        requiresAuth: boolean;
        isAuthenticated: boolean;
    };
    private formatModelName;
}
//# sourceMappingURL=configurationManager.d.ts.map