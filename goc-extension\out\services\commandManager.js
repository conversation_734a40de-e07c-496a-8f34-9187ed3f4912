"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommandManager = void 0;
const vscode = __importStar(require("vscode"));
class CommandManager {
    constructor(context, gocCoreService, chatProvider, statusBarManager, configurationManager) {
        this.context = context;
        this.gocCoreService = gocCoreService;
        this.chatProvider = chatProvider;
        this.statusBarManager = statusBarManager;
        this.configurationManager = configurationManager;
        this.disposables = [];
    }
    // Simple context manager methods
    getContextAroundSelection(document, selection) {
        const startLine = Math.max(0, selection.start.line - 5);
        const endLine = Math.min(document.lineCount - 1, selection.end.line + 5);
        const range = new vscode.Range(startLine, 0, endLine, document.lineAt(endLine).text.length);
        return document.getText(range);
    }
    getFileContext(document) {
        // Return first 100 lines as context
        const endLine = Math.min(document.lineCount - 1, 100);
        const range = new vscode.Range(0, 0, endLine, document.lineAt(endLine).text.length);
        return document.getText(range);
    }
    registerCommands() {
        // Chat commands
        this.registerCommand('gocAgent.openChat', () => this.openChat());
        // Code analysis commands
        this.registerCommand('gocAgent.explainCode', () => this.explainCode());
        this.registerCommand('gocAgent.generateCode', () => this.generateCode());
        this.registerCommand('gocAgent.fixIssues', () => this.fixIssues());
        this.registerCommand('gocAgent.refactorCode', () => this.refactorCode());
        this.registerCommand('gocAgent.generateTests', () => this.generateTests());
        // Project commands
        this.registerCommand('gocAgent.analyzeProject', () => this.analyzeProject());
        // Research commands
        this.registerCommand('gocAgent.webResearch', () => this.webResearch());
        this.registerCommand('gocAgent.trainTechnology', () => this.trainTechnology());
        // Configuration commands
        this.registerCommand('gocAgent.selectProvider', () => this.selectProvider());
        this.registerCommand('gocAgent.selectModel', () => this.selectModel());
        this.registerCommand('gocAgent.refreshOllamaModels', () => this.refreshOllamaModels());
        this.registerCommand('gocAgent.checkOllamaStatus', () => this.checkOllamaStatus());
        this.registerCommand('gocAgent.setupOllama', () => this.setupOllama());
        // Authentication commands
        this.registerCommand('gocAgent.login', () => this.login());
        this.registerCommand('gocAgent.register', () => this.register());
        this.registerCommand('gocAgent.logout', () => this.logout());
        this.registerCommand('gocAgent.showProfile', () => this.showProfile());
        this.registerCommand('gocAgent.checkAuthStatus', () => this.checkAuthStatus());
    }
    registerCommand(command, callback) {
        const disposable = vscode.commands.registerCommand(command, callback);
        this.disposables.push(disposable);
    }
    async openChat() {
        try {
            await vscode.commands.executeCommand('workbench.view.extension.gocAgent');
            await vscode.commands.executeCommand('gocAgent.chatView.focus');
        }
        catch (error) {
            console.error('Error opening chat:', error);
            vscode.window.showErrorMessage('Failed to open GOC Agent chat');
        }
    }
    async explainCode() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor found');
            return;
        }
        try {
            this.statusBarManager.updateStatus('working', 'Explaining code...');
            const selection = editor.selection;
            const document = editor.document;
            let codeToExplain;
            let context;
            if (!selection.isEmpty) {
                // Explain selected code
                codeToExplain = document.getText(selection);
                context = this.getContextAroundSelection(document, selection);
            }
            else {
                // Explain entire file
                codeToExplain = document.getText();
                context = this.getFileContext(document);
            }
            const explanation = await this.gocCoreService.explainCode(document.fileName, codeToExplain, context);
            // Show explanation in chat
            this.chatProvider.addMessage('assistant', explanation);
            await this.openChat();
        }
        catch (error) {
            this.handleError('Failed to explain code', error);
        }
        finally {
            this.statusBarManager.updateStatus('ready', 'GOC Agent is ready');
        }
    }
    async generateCode() {
        try {
            const prompt = await vscode.window.showInputBox({
                prompt: 'Describe the code you want to generate',
                placeHolder: 'e.g., Create a function that validates email addresses',
                validateInput: (value) => {
                    if (!value || value.trim().length === 0) {
                        return 'Please enter a description';
                    }
                    return null;
                }
            });
            if (!prompt)
                return;
            this.statusBarManager.updateStatus('working', 'Generating code...');
            const editor = vscode.window.activeTextEditor;
            const context = editor ? this.getFileContext(editor.document) : '';
            const generatedCode = await this.gocCoreService.generateCode(prompt, context);
            // Show generated code in chat
            this.chatProvider.addMessage('user', `Generate: ${prompt}`);
            this.chatProvider.addMessage('assistant', `\`\`\`\n${generatedCode}\n\`\`\``);
            await this.openChat();
        }
        catch (error) {
            this.handleError('Failed to generate code', error);
        }
        finally {
            this.statusBarManager.updateStatus('ready', 'GOC Agent is ready');
        }
    }
    async fixIssues() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor found');
            return;
        }
        try {
            this.statusBarManager.updateStatus('working', 'Fixing issues...');
            const code = editor.document.getText();
            const fixes = await this.gocCoreService.fixIssues(editor.document.fileName, code);
            // Show fixes in chat
            this.chatProvider.addMessage('assistant', fixes);
            await this.openChat();
        }
        catch (error) {
            this.handleError('Failed to fix issues', error);
        }
        finally {
            this.statusBarManager.updateStatus('ready', 'GOC Agent is ready');
        }
    }
    async refactorCode() {
        const editor = vscode.window.activeTextEditor;
        if (!editor || editor.selection.isEmpty) {
            vscode.window.showWarningMessage('Please select code to refactor');
            return;
        }
        try {
            const refactorType = await vscode.window.showQuickPick([
                { label: 'Extract Function', value: 'extract-function' },
                { label: 'Rename Variable', value: 'rename-variable' },
                { label: 'Simplify Logic', value: 'simplify' },
                { label: 'Optimize Performance', value: 'optimize' },
                { label: 'Improve Readability', value: 'readability' }
            ], {
                placeHolder: 'Select refactoring type'
            });
            if (!refactorType)
                return;
            this.statusBarManager.updateStatus('working', 'Refactoring code...');
            const selectedCode = editor.document.getText(editor.selection);
            const context = this.getContextAroundSelection(editor.document, editor.selection);
            // For now, use generate command with refactoring prompt
            const prompt = `Refactor this code (${refactorType.label}):\n\n${selectedCode}`;
            const refactoredCode = await this.gocCoreService.generateCode(prompt, context);
            // Show refactored code in chat
            this.chatProvider.addMessage('user', `Refactor (${refactorType.label})`);
            this.chatProvider.addMessage('assistant', `\`\`\`\n${refactoredCode}\n\`\`\``);
            await this.openChat();
        }
        catch (error) {
            this.handleError('Failed to refactor code', error);
        }
        finally {
            this.statusBarManager.updateStatus('ready', 'GOC Agent is ready');
        }
    }
    async generateTests() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor found');
            return;
        }
        try {
            this.statusBarManager.updateStatus('working', 'Generating tests...');
            const code = editor.selection.isEmpty
                ? editor.document.getText()
                : editor.document.getText(editor.selection);
            const prompt = `Generate unit tests for this code:\n\n${code}`;
            const context = this.getFileContext(editor.document);
            const tests = await this.gocCoreService.generateCode(prompt, context);
            // Show tests in chat
            this.chatProvider.addMessage('user', 'Generate tests');
            this.chatProvider.addMessage('assistant', `\`\`\`\n${tests}\n\`\`\``);
            await this.openChat();
        }
        catch (error) {
            this.handleError('Failed to generate tests', error);
        }
        finally {
            this.statusBarManager.updateStatus('ready', 'GOC Agent is ready');
        }
    }
    async analyzeProject() {
        try {
            this.statusBarManager.updateStatus('working', 'Analyzing project...');
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            if (!workspaceFolder) {
                vscode.window.showWarningMessage('No workspace folder found');
                return;
            }
            const analysis = await this.gocCoreService.analyzeProject(workspaceFolder.uri.fsPath);
            // Show analysis in chat
            this.chatProvider.addMessage('assistant', analysis);
            await this.openChat();
        }
        catch (error) {
            this.handleError('Failed to analyze project', error);
        }
        finally {
            this.statusBarManager.updateStatus('ready', 'GOC Agent is ready');
        }
    }
    async webResearch() {
        try {
            const query = await vscode.window.showInputBox({
                prompt: 'Enter your research query',
                placeHolder: 'e.g., How to implement OAuth in Node.js',
                validateInput: (value) => {
                    if (!value || value.trim().length === 0) {
                        return 'Please enter a query';
                    }
                    return null;
                }
            });
            if (!query)
                return;
            this.statusBarManager.updateStatus('working', 'Researching...');
            const results = await this.gocCoreService.webResearch(query);
            // Show research results in chat
            this.chatProvider.addMessage('user', `Research: ${query}`);
            this.chatProvider.addMessage('assistant', results);
            await this.openChat();
        }
        catch (error) {
            this.handleError('Failed to perform web research', error);
        }
        finally {
            this.statusBarManager.updateStatus('ready', 'GOC Agent is ready');
        }
    }
    async trainTechnology() {
        try {
            const technology = await vscode.window.showQuickPick([
                'JavaScript',
                'TypeScript',
                'Python',
                'PHP',
                'Laravel',
                'React',
                'Vue.js',
                'Node.js',
                'Flutter',
                'Custom...'
            ], {
                placeHolder: 'Select technology to train on'
            });
            if (!technology)
                return;
            let techName = technology;
            if (technology === 'Custom...') {
                const customTech = await vscode.window.showInputBox({
                    prompt: 'Enter technology name',
                    placeHolder: 'e.g., FastAPI, Django, etc.'
                });
                if (!customTech)
                    return;
                techName = customTech;
            }
            this.statusBarManager.updateStatus('working', `Training on ${techName}...`);
            // Use CLI training command
            await this.gocCoreService.runInteractiveCommand(['train', '-t', techName]);
        }
        catch (error) {
            this.handleError('Failed to start training', error);
        }
        finally {
            this.statusBarManager.updateStatus('ready', 'GOC Agent is ready');
        }
    }
    async selectProvider() {
        try {
            const selectedProvider = await this.configurationManager.selectProvider();
            if (selectedProvider) {
                this.statusBarManager.updateProviderInfo();
                vscode.window.showInformationMessage(`Switched to ${selectedProvider}`);
            }
        }
        catch (error) {
            this.handleError('Failed to select provider', error);
        }
    }
    async selectModel() {
        try {
            const selectedModel = await this.configurationManager.selectModel();
            if (selectedModel) {
                this.statusBarManager.updateProviderInfo();
                vscode.window.showInformationMessage(`Switched to ${selectedModel}`);
            }
        }
        catch (error) {
            this.handleError('Failed to select model', error);
        }
    }
    async refreshOllamaModels() {
        try {
            this.statusBarManager.updateStatus('working', 'Refreshing Ollama models...');
            await this.configurationManager.refreshOllamaModels();
            const models = await this.configurationManager.getModelsByProvider('ollama');
            if (models.length > 0) {
                vscode.window.showInformationMessage(`Found ${models.length} Ollama models: ${models.slice(0, 3).join(', ')}${models.length > 3 ? '...' : ''}`);
            }
            else {
                vscode.window.showWarningMessage('No Ollama models found. Install models with: ollama pull llama3.2:3b');
            }
        }
        catch (error) {
            this.handleError('Failed to refresh Ollama models', error);
        }
        finally {
            this.statusBarManager.updateStatus('ready', 'GOC Agent is ready');
        }
    }
    async checkOllamaStatus() {
        try {
            const status = await this.configurationManager.checkOllamaStatus();
            if (!status.isRunning) {
                vscode.window.showErrorMessage('Ollama is not running. Please start Ollama first.');
            }
            else if (!status.hasModels) {
                vscode.window.showWarningMessage('Ollama is running but no models are installed.');
            }
            else {
                vscode.window.showInformationMessage(`Ollama Status: ✅ Running with ${status.models.length} models available`);
            }
        }
        catch (error) {
            this.handleError('Failed to check Ollama status', error);
        }
    }
    async setupOllama() {
        try {
            await this.configurationManager.showOllamaSetupDialog();
        }
        catch (error) {
            this.handleError('Failed to show Ollama setup', error);
        }
    }
    async login() {
        try {
            await this.configurationManager.showLoginDialog();
            this.statusBarManager.updateProviderInfo();
        }
        catch (error) {
            this.handleError('Failed to login', error);
        }
    }
    async register() {
        try {
            await this.configurationManager.showRegistrationDialog();
            this.statusBarManager.updateProviderInfo();
        }
        catch (error) {
            this.handleError('Failed to register', error);
        }
    }
    async logout() {
        try {
            await this.configurationManager.logout();
            this.statusBarManager.updateProviderInfo();
        }
        catch (error) {
            this.handleError('Failed to logout', error);
        }
    }
    async showProfile() {
        try {
            await this.configurationManager.showUserProfile();
        }
        catch (error) {
            this.handleError('Failed to show profile', error);
        }
    }
    async checkAuthStatus() {
        try {
            const status = this.configurationManager.getDetailedStatus();
            if (status.requiresAuth) {
                if (status.isAuthenticated) {
                    vscode.window.showInformationMessage(`✅ Authenticated with ${status.provider} (${status.model})`, 'View Profile').then(selection => {
                        if (selection === 'View Profile') {
                            this.showProfile();
                        }
                    });
                }
                else {
                    vscode.window.showWarningMessage(`❌ Not authenticated with ${status.provider}`, 'Login', 'Register').then(selection => {
                        if (selection === 'Login') {
                            this.login();
                        }
                        else if (selection === 'Register') {
                            this.register();
                        }
                    });
                }
            }
            else {
                vscode.window.showInformationMessage(`✅ Using ${status.provider} (${status.model}) - No authentication required`);
            }
        }
        catch (error) {
            this.handleError('Failed to check authentication status', error);
        }
    }
    handleError(message, error) {
        console.error(message, error);
        this.statusBarManager.showError(message);
        vscode.window.showErrorMessage(`${message}: ${error.message || error}`);
    }
    getDisposables() {
        return [...this.disposables];
    }
    dispose() {
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}
exports.CommandManager = CommandManager;
//# sourceMappingURL=commandManager.js.map