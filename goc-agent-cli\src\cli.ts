#!/usr/bin/env node

/**
 * GOC Agent CLI
 * 
 * Command-line interface powered by GOC Core
 */

import { Command } from 'commander';
import chalk from 'chalk';
import boxen from 'boxen';
import ora from 'ora';
import { createG<PERSON><PERSON><PERSON>, GocCore, ConfigManager, logger } from '@goc-agent/core';
import { ChatCommand } from './commands/chat';
import { AgentCommand } from './commands/agent';
import { ConfigCommand } from './commands/config';
import { AnalyzeCommand } from './commands/analyze';
import { TrainCommand } from './commands/train';
import { ResearchCommand } from './commands/research';
import { AuthCommand } from './commands/auth';
import { SessionCommand } from './commands/session';
import { ToolsCommand } from './commands/tools';
import { ContextCommand } from './commands/context';
import { WebCommand } from './commands/web';
import { AutoCommand } from './commands/auto';
import { GenerateCommand } from './commands/generate';
import { MonitorCommand } from './commands/monitor';
import { DocsCommand } from './commands/docs';
import { DeployCommand } from './commands/deploy';
import { ModelsCommand } from './commands/models';
import { BackendAPIClient } from '../services/BackendAPIClient';

class GocCLI {
  private program: Command;
  private core: GocCore | null = null;
  private configManager: ConfigManager | null = null;
  private apiClient: BackendAPIClient;

  constructor() {
    this.program = new Command();
    this.apiClient = new BackendAPIClient();
    this.setupProgram();
  }

  private setupProgram(): void {
    this.program
      .name('goc')
      .description('GOC Agent - AI-powered coding assistant')
      .version('1.0.0')
      .option('-v, --verbose', 'Enable verbose output')
      .option('-q, --quiet', 'Suppress non-essential output')
      .hook('preAction', async (thisCommand) => {
        await this.initialize(thisCommand.opts());
        // Register commands after initialization
        this.registerCommands();
      });
  }

  private registerCommands(): void {
    // Only register commands if core is initialized
    if (!this.core || !this.configManager) {
      return;
    }

    // Register authentication command
    const authCommand = new AuthCommand(this.apiClient);
    authCommand.registerCommands(this.program);

    // Register session management command
    const sessionCommand = new SessionCommand(this.apiClient);
    sessionCommand.register(this.program);

    // Register tools command
    const toolsCommand = new ToolsCommand();
    toolsCommand.register(this.program);

    // Register context command
    const contextCommand = new ContextCommand(this.core);
    contextCommand.register(this.program);

    // Register web command
    const webCommand = new WebCommand(this.core);
    webCommand.register(this.program);

    // Register auto mode command
    const autoCommand = new AutoCommand(this.core);
    autoCommand.register(this.program);

    // Register code generation command
    const generateCommand = new GenerateCommand(this.core);
    generateCommand.register(this.program);

    // Register monitoring command
    const monitorCommand = new MonitorCommand(this.core);
    monitorCommand.register(this.program);

    // Register documentation command
    const docsCommand = new DocsCommand(this.core);
    docsCommand.register(this.program);

    // Register deployment command
    const deployCommand = new DeployCommand(this.core);
    deployCommand.register(this.program);

    // Register other commands
    const chatCommand = new ChatCommand(this.core, this.configManager, this.apiClient);
    chatCommand.register(this.program);
    const agentCommand = new AgentCommand(this.core, this.configManager);
    agentCommand.register(this.program);
    const configCommand = new ConfigCommand();
    configCommand.register(this.program);

    const analyzeCommand = new AnalyzeCommand(this.core);
    analyzeCommand.register(this.program);

    const trainCommand = new TrainCommand(this.core);
    trainCommand.register(this.program);

    const researchCommand = new ResearchCommand(this.core);
    researchCommand.register(this.program);

    // Register models command
    const modelsCommand = new ModelsCommand();
    modelsCommand.register(this.program);

    // Status command
    this.program
      .command('status')
      .description('Show GOC Agent status')
      .action(async () => {
        await this.showStatus();
      });
  }

  private async initialize(options: any): Promise<void> {
    try {
      // Configure logger
      logger.updateConfig({
        level: options.verbose ? 'debug' : options.quiet ? 'error' : 'info',
        enableConsole: true
      });

      // Show initialization message
      if (!options.quiet) {
        const spinner = ora('Initializing GOC Agent...').start();
        
        try {
          // Initialize configuration
          this.configManager = new ConfigManager();
          await this.configManager.initialize();

          // Initialize core
          this.core = await createGocCore({
            ai: {
              defaultProvider: this.configManager.getConfig().ai.defaultProvider,
              defaultModel: this.configManager.getConfig().ai.defaultModel
            },
            global: {
              logLevel: options.verbose ? 'debug' : 'info'
            }
          });

          spinner.succeed('GOC Agent initialized successfully');
        } catch (error) {
          spinner.fail('Failed to initialize GOC Agent');
          throw error;
        }
      } else {
        // Silent initialization
        this.configManager = new ConfigManager();
        await this.configManager.initialize();
        
        this.core = await createGocCore({
          ai: {
            defaultProvider: this.configManager.getConfig().ai.defaultProvider,
            defaultModel: this.configManager.getConfig().ai.defaultModel
          }
        });
      }

    } catch (error) {
      console.error(chalk.red('Failed to initialize GOC Agent:'), error);
      process.exit(1);
    }
  }

  private async showStatus(): Promise<void> {
    if (!this.core || !this.configManager) {
      console.error(chalk.red('GOC Agent not initialized'));
      return;
    }

    const config = this.configManager.getConfig();
    const isInitialized = this.core.isInitialized();

    // Check backend connectivity
    const backendHealthy = await this.apiClient.healthCheck();
    const isAuthenticated = this.apiClient.isAuthenticated();

    const statusInfo = [
      ['Status', isInitialized ? chalk.green('Ready') : chalk.red('Not Ready')],
      ['Version', '1.0.0'],
      ['Provider', config.ai.defaultProvider],
      ['Model', config.ai.defaultModel],
      ['Backend', backendHealthy ? chalk.green('Connected') : chalk.yellow('Offline')],
      ['Authentication', isAuthenticated ? chalk.green('Authenticated') : chalk.red('Not Authenticated')],
      ['Config Path', '~/.goc-agent/config.yaml']
    ];

    console.log(boxen(
      statusInfo.map(([key, value]) => `${chalk.bold(key)}: ${value}`).join('\n'),
      {
        title: 'GOC Agent Status',
        padding: 1,
        borderStyle: 'round',
        borderColor: 'blue'
      }
    ));

    if (!backendHealthy) {
      console.log(chalk.yellow('\n⚠️  Backend is offline. Some features may not be available.'));
      console.log(chalk.dim('   Make sure the Laravel backend is running on http://localhost:8000'));
    }

    if (!isAuthenticated && backendHealthy) {
      console.log(chalk.blue('\n💡 Tip: Use "goc auth login" to authenticate and access cloud features.'));
    }
  }

  async run(): Promise<void> {
    try {
      await this.program.parseAsync(process.argv);
    } catch (error) {
      logger.error('CLI execution failed', error as Error);
      console.error(chalk.red('Error:'), error);
      process.exit(1);
    } finally {
      // Cleanup
      if (this.core) {
        await this.core.dispose();
      }
    }
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception', error);
  console.error(chalk.red('Uncaught exception:'), error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled rejection', reason as Error);
  console.error(chalk.red('Unhandled rejection:'), reason);
  process.exit(1);
});

// Run CLI
if (require.main === module) {
  const cli = new GocCLI();
  cli.run();
}
