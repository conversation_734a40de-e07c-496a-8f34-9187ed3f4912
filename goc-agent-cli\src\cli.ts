#!/usr/bin/env node

/**
 * GOC Agent CLI
 * 
 * Command-line interface powered by GOC Core
 */

import { Command } from 'commander';
import chalk from 'chalk';
import boxen from 'boxen';
import ora from 'ora';
import { createGoc<PERSON>ore, GocCore, ConfigManager, logger } from '@goc-agent/core';
import { ProductionConfigManager } from './config/production';
import { EnvironmentLoader } from './config/environment';
import { ChatCommand } from './commands/chat';
import { AgentCommand } from './commands/agent';
import { ConfigCommand } from './commands/config';
import { AnalyzeCommand } from './commands/analyze';
import { TrainCommand } from './commands/train';
import { ResearchCommand } from './commands/research';
import { AuthCommand } from './commands/auth';
import { SessionCommand } from './commands/session';
import { ToolsCommand } from './commands/tools';
import { ContextCommand } from './commands/context';
import { WebCommand } from './commands/web';
import { AutoCommand } from './commands/auto';
import { GenerateCommand } from './commands/generate';
import { MonitorCommand } from './commands/monitor';
import { DocsCommand } from './commands/docs';
import { DeployCommand } from './commands/deploy';
import { ModelsCommand } from './commands/models';
import { BackendAPIClient } from '../services/BackendAPIClient';

class GocCLI {
  private program: Command;
  private core: GocCore | null = null;
  private configManager: ConfigManager | null = null;
  private productionConfig: ProductionConfigManager | null = null;
  private environmentLoader: EnvironmentLoader | null = null;
  private apiClient: BackendAPIClient;

  constructor() {
    this.program = new Command();
    this.apiClient = new BackendAPIClient();
    this.setupProgram();
  }

  private setupProgram(): void {
    this.program
      .name('goc')
      .description('GOC Agent - AI-powered coding assistant')
      .version('1.0.0')
      .option('-v, --verbose', 'Enable verbose output')
      .option('-q, --quiet', 'Suppress non-essential output')
      .hook('preAction', async (thisCommand) => {
        await this.initialize(thisCommand.opts());
      });

    // Register all commands (structure first, then connect to core after init)
    this.registerAllCommands();
  }

  private registerAllCommands(): void {
    // Register commands that don't need core initialization

    // Register authentication command
    const authCommand = new AuthCommand(this.apiClient);
    authCommand.registerCommands(this.program);

    // Register session management command
    const sessionCommand = new SessionCommand(this.apiClient);
    sessionCommand.register(this.program);

    // Register tools command
    const toolsCommand = new ToolsCommand();
    toolsCommand.register(this.program);

    // Register config command
    const configCommand = new ConfigCommand();
    configCommand.register(this.program);

    // Register models command
    const modelsCommand = new ModelsCommand();
    modelsCommand.register(this.program);

    // Status command
    this.program
      .command('status')
      .description('Show GOC Agent status')
      .action(async () => {
        await this.showStatus();
      });

    // Production config command
    this.program
      .command('production')
      .description('Production configuration management')
      .addCommand(
        this.program.createCommand('status')
          .description('Show production configuration status')
          .action(async () => {
            await this.showProductionStatus();
          })
      )
      .addCommand(
        this.program.createCommand('health')
          .description('Run production health check')
          .action(async () => {
            await this.runProductionHealthCheck();
          })
      )
      .addCommand(
        this.program.createCommand('env-example')
          .description('Generate example .env file')
          .action(async () => {
            await this.generateEnvExample();
          })
      );

    // Register intelligent commands with lazy initialization
    this.registerIntelligentCommands();
  }

  private registerIntelligentCommands(): void {
    // Register context command with lazy core access
    this.program
      .command('context')
      .description('Intelligent context analysis and search')
      .addCommand(
        this.program.createCommand('analyze')
          .argument('<query>', 'Query to analyze')
          .option('-p, --project <path>', 'Project path')
          .action(async (query, options) => {
            await this.ensureInitialized();
            const contextCommand = new ContextCommand(this.core!);
            await contextCommand.analyzeContext(query, options);
          })
      )
      .addCommand(
        this.program.createCommand('search')
          .argument('<query>', 'Search query')
          .option('-l, --limit <number>', 'Max results', '10')
          .action(async (query, options) => {
            await this.ensureInitialized();
            const contextCommand = new ContextCommand(this.core!);
            await contextCommand.searchContext(query, options);
          })
      );

    // Register web command with lazy core access
    this.program
      .command('web')
      .description('Intelligent web research and content processing')
      .addCommand(
        this.program.createCommand('research')
          .argument('<topic>', 'Topic to research')
          .option('-d, --depth <level>', 'Research depth (basic|intermediate|advanced)', 'intermediate')
          .option('-s, --sources <number>', 'Maximum sources', '10')
          .option('--examples', 'Include code examples')
          .option('--trends', 'Include trend analysis')
          .action(async (topic, options) => {
            await this.ensureInitialized();
            const webCommand = new WebCommand(this.core!);
            await webCommand.researchTopic(topic, options);
          })
      )
      .addCommand(
        this.program.createCommand('search')
          .argument('<query>', 'Search query')
          .option('-m, --max <number>', 'Maximum results', '10')
          .option('-l, --language <lang>', 'Search language', 'en')
          .action(async (query, options) => {
            await this.ensureInitialized();
            const webCommand = new WebCommand(this.core!);
            await webCommand.searchWeb(query, options);
          })
      );

    // Register auto mode command
    this.program
      .command('auto')
      .description('Autonomous intelligent task execution')
      .addCommand(
        this.program.createCommand('execute')
          .argument('<goal>', 'Goal to execute')
          .option('-t, --timeout <minutes>', 'Timeout in minutes', '30')
          .option('--safe', 'Enable safe mode')
          .action(async (goal, options) => {
            await this.ensureInitialized();
            const autoCommand = new AutoCommand(this.core!);
            await autoCommand.executeGoal(goal, options);
          })
      );

    // Register chat command
    this.program
      .command('chat')
      .description('Interactive AI chat with learning')
      .option('-m, --model <model>', 'AI model to use')
      .option('-p, --provider <provider>', 'AI provider to use')
      .action(async (options) => {
        await this.ensureInitialized();
        const chatCommand = new ChatCommand(this.core!, this.configManager!, this.apiClient);
        await chatCommand.startInteractiveChat(options);
      });

    // Register analyze command
    this.program
      .command('analyze')
      .description('Analyze code and projects')
      .argument('[path]', 'Path to analyze', '.')
      .option('--metrics', 'Include code metrics')
      .option('--suggestions', 'Include improvement suggestions')
      .action(async (path, options) => {
        await this.ensureInitialized();
        const analyzeCommand = new AnalyzeCommand(this.core!);
        await analyzeCommand.analyzeProject(path, options);
      });

    // Register generate command
    this.program
      .command('generate')
      .description('Generate code using AI')
      .addCommand(
        this.program.createCommand('code')
          .argument('<description>', 'Code description')
          .option('-l, --language <lang>', 'Programming language')
          .option('-f, --file <path>', 'Output file path')
          .action(async (description, options) => {
            await this.ensureInitialized();
            const generateCommand = new GenerateCommand(this.core!);
            await generateCommand.generateCode(description, options);
          })
      )
      .addCommand(
        this.program.createCommand('tests')
          .argument('<filePath>', 'File to generate tests for')
          .option('-f, --framework <framework>', 'Test framework')
          .action(async (filePath, options) => {
            await this.ensureInitialized();
            const generateCommand = new GenerateCommand(this.core!);
            await generateCommand.generateTests(filePath, options);
          })
      );

    // Register documentation command
    this.program
      .command('docs')
      .description('Generate documentation')
      .addCommand(
        this.program.createCommand('generate')
          .argument('[path]', 'Path to document', '.')
          .option('-f, --format <format>', 'Output format (md|html|pdf)', 'md')
          .action(async (path, options) => {
            await this.ensureInitialized();
            const docsCommand = new DocsCommand(this.core!);
            await docsCommand.generateDocs(path, options);
          })
      );

    // Register deployment command
    this.program
      .command('deploy')
      .description('Deployment assistance')
      .addCommand(
        this.program.createCommand('plan')
          .argument('<environment>', 'Target environment')
          .action(async (environment, options) => {
            await this.ensureInitialized();
            const deployCommand = new DeployCommand(this.core!);
            await deployCommand.createDeploymentPlan(environment, options);
          })
      );

    // Register monitoring command
    this.program
      .command('monitor')
      .description('Monitor system and learning')
      .addCommand(
        this.program.createCommand('status')
          .action(async () => {
            await this.ensureInitialized();
            const monitorCommand = new MonitorCommand(this.core!);
            await monitorCommand.showStatus();
          })
      )
      .addCommand(
        this.program.createCommand('metrics')
          .action(async () => {
            await this.ensureInitialized();
            const monitorCommand = new MonitorCommand(this.core!);
            await monitorCommand.showMetrics({});
          })
      );

    // Register training command
    this.program
      .command('train')
      .description('Training and learning management')
      .addCommand(
        this.program.createCommand('status')
          .action(async () => {
            await this.ensureInitialized();
            const trainCommand = new TrainCommand(this.core!);
            await trainCommand.showTrainingStatus();
          })
      )
      .addCommand(
        this.program.createCommand('technology')
          .argument('<technology>', 'Technology to learn')
          .action(async (technology, options) => {
            await this.ensureInitialized();
            const trainCommand = new TrainCommand(this.core!);
            await trainCommand.trainTechnology(technology, options);
          })
      );
  }

  private async ensureInitialized(): Promise<void> {
    if (!this.core || !this.configManager) {
      throw new Error('GOC Agent not initialized. This should not happen.');
    }
  }

  private async applyEnvironmentOverrides(envConfig: any): Promise<void> {
    if (!this.productionConfig) return;

    const updates: any = {};

    // Apply API keys
    if (envConfig.GOC_API_KEY) {
      updates.ai = { ...this.productionConfig.getConfig().ai };
      updates.ai.providers = { ...updates.ai.providers };
      updates.ai.providers.goc = { ...updates.ai.providers.goc, apiKey: envConfig.GOC_API_KEY };
    }

    // Apply provider URLs
    if (envConfig.OLLAMA_BASE_URL) {
      if (!updates.ai) updates.ai = { ...this.productionConfig.getConfig().ai };
      if (!updates.ai.providers) updates.ai.providers = { ...updates.ai.providers };
      updates.ai.providers.ollama = { ...updates.ai.providers.ollama, baseUrl: envConfig.OLLAMA_BASE_URL };
    }

    if (envConfig.GOC_BASE_URL) {
      if (!updates.ai) updates.ai = { ...this.productionConfig.getConfig().ai };
      if (!updates.ai.providers) updates.ai.providers = { ...updates.ai.providers };
      updates.ai.providers.goc = { ...updates.ai.providers.goc, baseUrl: envConfig.GOC_BASE_URL };
    }

    // Apply feature flags
    if (envConfig.GOC_WEB_RESEARCH !== undefined) {
      updates.features = { ...this.productionConfig.getConfig().features };
      updates.features.webResearch = this.environmentLoader!.getBooleanValue('GOC_WEB_RESEARCH', true);
    }

    if (envConfig.GOC_AUTO_TRAINING !== undefined) {
      if (!updates.features) updates.features = { ...this.productionConfig.getConfig().features };
      updates.features.autoTraining = this.environmentLoader!.getBooleanValue('GOC_AUTO_TRAINING', true);
    }

    if (envConfig.GOC_TELEMETRY !== undefined) {
      if (!updates.features) updates.features = { ...this.productionConfig.getConfig().features };
      updates.features.telemetry = this.environmentLoader!.getBooleanValue('GOC_TELEMETRY', false);
    }

    // Apply performance settings
    if (envConfig.GOC_MAX_MEMORY) {
      updates.performance = { ...this.productionConfig.getConfig().performance };
      updates.performance.maxMemoryUsage = this.environmentLoader!.getNumberValue('GOC_MAX_MEMORY', 1024);
    }

    if (envConfig.GOC_REQUEST_TIMEOUT) {
      if (!updates.performance) updates.performance = { ...this.productionConfig.getConfig().performance };
      updates.performance.requestTimeout = this.environmentLoader!.getNumberValue('GOC_REQUEST_TIMEOUT', 30000);
    }

    // Apply logging settings
    if (envConfig.GOC_LOG_LEVEL) {
      updates.logging = { ...this.productionConfig.getConfig().logging };
      updates.logging.level = this.environmentLoader!.getStringValue('GOC_LOG_LEVEL', 'info') as any;
    }

    // Apply environment
    if (envConfig.GOC_ENV) {
      updates.environment = this.environmentLoader!.getStringValue('GOC_ENV', 'production') as any;
    }

    // Apply security settings
    if (envConfig.GOC_ENCRYPT_KEYS !== undefined) {
      updates.security = { ...this.productionConfig.getConfig().security };
      updates.security.encryptApiKeys = this.environmentLoader!.getBooleanValue('GOC_ENCRYPT_KEYS', true);
    }

    if (envConfig.GOC_VALIDATE_CERTS !== undefined) {
      if (!updates.security) updates.security = { ...this.productionConfig.getConfig().security };
      updates.security.validateCertificates = this.environmentLoader!.getBooleanValue('GOC_VALIDATE_CERTS', true);
    }

    // Apply updates if any
    if (Object.keys(updates).length > 0) {
      await this.productionConfig.updateConfig(updates);
    }
  }

  private async initialize(options: any): Promise<void> {
    try {
      // Load environment configuration first
      this.environmentLoader = new EnvironmentLoader();
      const envConfig = await this.environmentLoader.load();

      // Initialize production configuration
      this.productionConfig = new ProductionConfigManager();
      await this.productionConfig.initialize();

      // Apply environment overrides to production config
      await this.applyEnvironmentOverrides(envConfig);

      // Configure logger based on production config
      const prodConfig = this.productionConfig.getConfig();
      logger.updateConfig({
        level: options.verbose ? 'debug' : options.quiet ? 'error' : prodConfig.logging.level,
        enableConsole: true,
        enableFileLogging: prodConfig.logging.enableFileLogging,
        logDirectory: prodConfig.logging.logDirectory
      });

      // Show initialization message
      if (!options.quiet) {
        const spinner = ora('Initializing GOC Agent...').start();

        try {
          // Initialize configuration
          this.configManager = new ConfigManager();
          await this.configManager.initialize();

          // Initialize core with production settings
          this.core = await createGocCore({
            ai: {
              defaultProvider: prodConfig.ai.defaultProvider,
              defaultModel: prodConfig.ai.defaultModel
            },
            global: {
              logLevel: options.verbose ? 'debug' : prodConfig.logging.level,
              maxMemoryUsage: prodConfig.performance.maxMemoryUsage,
              requestTimeout: prodConfig.performance.requestTimeout
            },
            features: {
              webResearch: prodConfig.features.webResearch,
              autoTraining: prodConfig.features.autoTraining,
              contextEngine: prodConfig.features.contextEngine,
              autoMode: prodConfig.features.autoMode
            }
          });

          spinner.succeed('GOC Agent initialized successfully');
        } catch (error) {
          spinner.fail('Failed to initialize GOC Agent');
          throw error;
        }
      } else {
        // Silent initialization
        this.configManager = new ConfigManager();
        await this.configManager.initialize();

        this.core = await createGocCore({
          ai: {
            defaultProvider: prodConfig.ai.defaultProvider,
            defaultModel: prodConfig.ai.defaultModel
          },
          global: {
            logLevel: prodConfig.logging.level,
            maxMemoryUsage: prodConfig.performance.maxMemoryUsage,
            requestTimeout: prodConfig.performance.requestTimeout
          },
          features: {
            webResearch: prodConfig.features.webResearch,
            autoTraining: prodConfig.features.autoTraining,
            contextEngine: prodConfig.features.contextEngine,
            autoMode: prodConfig.features.autoMode
          }
        });
      }

    } catch (error) {
      console.error(chalk.red('Failed to initialize GOC Agent:'), error);
      process.exit(1);
    }
  }

  private async showStatus(): Promise<void> {
    if (!this.core || !this.configManager) {
      console.error(chalk.red('GOC Agent not initialized'));
      return;
    }

    const config = this.configManager.getConfig();
    const isInitialized = this.core.isInitialized();

    // Check backend connectivity
    const backendHealthy = await this.apiClient.healthCheck();
    const isAuthenticated = this.apiClient.isAuthenticated();

    const statusInfo = [
      ['Status', isInitialized ? chalk.green('Ready') : chalk.red('Not Ready')],
      ['Version', '1.0.0'],
      ['Provider', config.ai.defaultProvider],
      ['Model', config.ai.defaultModel],
      ['Backend', backendHealthy ? chalk.green('Connected') : chalk.yellow('Offline')],
      ['Authentication', isAuthenticated ? chalk.green('Authenticated') : chalk.red('Not Authenticated')],
      ['Config Path', '~/.goc-agent/config.yaml']
    ];

    console.log(boxen(
      statusInfo.map(([key, value]) => `${chalk.bold(key)}: ${value}`).join('\n'),
      {
        title: 'GOC Agent Status',
        padding: 1,
        borderStyle: 'round',
        borderColor: 'blue'
      }
    ));

    if (!backendHealthy) {
      console.log(chalk.yellow('\n⚠️  Backend is offline. Some features may not be available.'));
      console.log(chalk.dim('   Make sure the Laravel backend is running on http://localhost:8000'));
    }

    if (!isAuthenticated && backendHealthy) {
      console.log(chalk.blue('\n💡 Tip: Use "goc auth login" to authenticate and access cloud features.'));
    }
  }

  private async showProductionStatus(): Promise<void> {
    if (!this.productionConfig) {
      console.error(chalk.red('Production configuration not initialized'));
      return;
    }

    const config = this.productionConfig.getConfig();

    const statusInfo = [
      ['Environment', config.environment],
      ['Default Provider', config.ai.defaultProvider],
      ['Default Model', config.ai.defaultModel],
      ['Web Research', config.features.webResearch ? chalk.green('Enabled') : chalk.red('Disabled')],
      ['Auto Training', config.features.autoTraining ? chalk.green('Enabled') : chalk.red('Disabled')],
      ['Context Engine', config.features.contextEngine ? chalk.green('Enabled') : chalk.red('Disabled')],
      ['Auto Mode', config.features.autoMode ? chalk.green('Enabled') : chalk.red('Disabled')],
      ['Telemetry', config.features.telemetry ? chalk.yellow('Enabled') : chalk.green('Disabled')],
      ['Max Memory', `${config.performance.maxMemoryUsage} MB`],
      ['Request Timeout', `${config.performance.requestTimeout} ms`],
      ['Log Level', config.logging.level],
      ['File Logging', config.logging.enableFileLogging ? chalk.green('Enabled') : chalk.red('Disabled')]
    ];

    console.log(boxen(
      statusInfo.map(([key, value]) => `${chalk.bold(key)}: ${value}`).join('\n'),
      {
        title: 'Production Configuration',
        padding: 1,
        borderStyle: 'round',
        borderColor: 'green'
      }
    ));

    // Show provider status
    console.log(chalk.blue('\n🔌 AI Providers:'));
    console.log(`  Ollama: ${config.ai.providers.ollama.enabled ? chalk.green('Enabled') : chalk.red('Disabled')} (${config.ai.providers.ollama.baseUrl})`);
    console.log(`  GOC Agent: ${config.ai.providers.goc.enabled ? chalk.green('Enabled') : chalk.red('Disabled')} (${config.ai.providers.goc.baseUrl})`);

    if (config.ai.providers.goc.apiKey) {
      console.log(`  GOC API Key: ${chalk.green('Configured')} (Tier: ${config.ai.providers.goc.tier})`);
    } else {
      console.log(`  GOC API Key: ${chalk.yellow('Not configured')}`);
    }
  }

  private async runProductionHealthCheck(): Promise<void> {
    if (!this.productionConfig) {
      console.error(chalk.red('Production configuration not initialized'));
      return;
    }

    console.log(chalk.blue('🔍 Running production health check...\n'));

    const healthResult = await this.productionConfig.healthCheck();

    console.log(`Overall Status: ${healthResult.status === 'healthy' ? chalk.green('Healthy') : chalk.yellow('Warning')}`);

    if (healthResult.issues.length > 0) {
      console.log(chalk.yellow('\n⚠️  Issues found:'));
      healthResult.issues.forEach(issue => {
        console.log(`  • ${issue}`);
      });
    } else {
      console.log(chalk.green('\n✅ No issues found'));
    }

    // Check environment configuration
    if (this.environmentLoader) {
      const apiKeyValidation = this.environmentLoader.validateApiKeys();
      if (!apiKeyValidation.valid) {
        console.log(chalk.yellow('\n⚠️  API Key Issues:'));
        apiKeyValidation.missing.forEach(missing => {
          console.log(`  • ${missing}`);
        });
      }

      const securityWarnings = this.environmentLoader.checkSensitiveDataExposure();
      if (securityWarnings.length > 0) {
        console.log(chalk.red('\n🔒 Security Warnings:'));
        securityWarnings.forEach(warning => {
          console.log(`  • ${warning}`);
        });
      }
    }

    // Memory check
    const memUsage = process.memoryUsage();
    const memUsageMB = Math.round(memUsage.heapUsed / 1024 / 1024);
    const maxMemory = this.productionConfig.getConfig().performance.maxMemoryUsage;

    console.log(chalk.blue('\n📊 Performance Metrics:'));
    console.log(`  Memory Usage: ${memUsageMB} MB / ${maxMemory} MB (${Math.round((memUsageMB / maxMemory) * 100)}%)`);

    if (memUsageMB > maxMemory * 0.8) {
      console.log(chalk.yellow('  ⚠️  Memory usage is high'));
    } else {
      console.log(chalk.green('  ✅ Memory usage is normal'));
    }
  }

  private async generateEnvExample(): Promise<void> {
    if (!this.environmentLoader) {
      console.error(chalk.red('Environment loader not initialized'));
      return;
    }

    const exampleEnv = this.environmentLoader.generateExampleEnv();

    console.log(chalk.blue('📝 Example .env file:\n'));
    console.log(exampleEnv);

    console.log(chalk.blue('\n💡 To use this configuration:'));
    console.log('1. Copy the content above to a .env file in your project root');
    console.log('2. Fill in your API keys and adjust settings as needed');
    console.log('3. Restart GOC Agent to apply the new configuration');
  }

  async run(): Promise<void> {
    try {
      await this.program.parseAsync(process.argv);
    } catch (error) {
      logger.error('CLI execution failed', error as Error);
      console.error(chalk.red('Error:'), error);
      process.exit(1);
    } finally {
      // Cleanup
      if (this.core) {
        await this.core.dispose();
      }
    }
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception', error);
  console.error(chalk.red('Uncaught exception:'), error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled rejection', reason as Error);
  console.error(chalk.red('Unhandled rejection:'), reason);
  process.exit(1);
});

// Run CLI
if (require.main === module) {
  const cli = new GocCLI();
  cli.run();
}
