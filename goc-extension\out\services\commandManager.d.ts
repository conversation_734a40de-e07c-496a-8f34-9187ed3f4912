import * as vscode from 'vscode';
import { GocCoreService } from './gocCoreService';
import { ChatProvider } from '../providers/chatProvider';
import { StatusBarManager } from './statusBarManager';
import { ConfigurationManager } from './configurationManager';
export declare class CommandManager implements vscode.Disposable {
    private context;
    private gocCoreService;
    private chatProvider;
    private statusBarManager;
    private configurationManager;
    private disposables;
    constructor(context: vscode.ExtensionContext, gocCoreService: GocCoreService, chatProvider: ChatProvider, statusBarManager: StatusBarManager, configurationManager: ConfigurationManager);
    private getContextAroundSelection;
    private getFileContext;
    registerCommands(): void;
    private registerCommand;
    private openChat;
    private explainCode;
    private generateCode;
    private fixIssues;
    private refactorCode;
    private generateTests;
    private analyzeProject;
    private webResearch;
    private trainTechnology;
    private selectProvider;
    private selectModel;
    private refreshOllamaModels;
    private checkOllamaStatus;
    private setupOllama;
    private login;
    private register;
    private logout;
    private showProfile;
    private checkAuthStatus;
    private handleError;
    getDisposables(): vscode.Disposable[];
    dispose(): void;
}
//# sourceMappingURL=commandManager.d.ts.map