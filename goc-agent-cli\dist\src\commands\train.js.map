{"version": 3, "file": "train.js", "sourceRoot": "", "sources": ["../../../src/commands/train.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;AAIH,kDAA0B;AAC1B,8CAAsB;AAEtB,MAAa,YAAY;IAGvB,YAAY,IAAa;QACvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAED,QAAQ,CAAC,OAAgB;QACvB,MAAM,QAAQ,GAAG,OAAO;aACrB,OAAO,CAAC,OAAO,CAAC;aAChB,WAAW,CAAC,6BAA6B,CAAC,CAAC;QAE9C,QAAQ;aACL,OAAO,CAAC,OAAO,CAAC;aAChB,WAAW,CAAC,wBAAwB,CAAC;aACrC,MAAM,CAAC,yBAAyB,EAAE,gDAAgD,CAAC;aACnF,MAAM,CAAC,uBAAuB,EAAE,mCAAmC,EAAE,GAAG,CAAC;aACzE,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACxB,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEL,QAAQ;aACL,OAAO,CAAC,QAAQ,CAAC;aACjB,WAAW,CAAC,sBAAsB,CAAC;aACnC,MAAM,CAAC,KAAK,IAAI,EAAE;YACjB,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAClC,CAAC,CAAC,CAAC;QAEL,QAAQ;aACL,OAAO,CAAC,SAAS,CAAC;aAClB,WAAW,CAAC,uBAAuB,CAAC;aACpC,MAAM,CAAC,KAAK,IAAI,EAAE;YACjB,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,OAAY;QACtC,MAAM,OAAO,GAAG,IAAA,aAAG,EAAC,8BAA8B,CAAC,CAAC,KAAK,EAAE,CAAC;QAE5D,IAAI,CAAC;YACH,OAAO,CAAC,IAAI,GAAG,uBAAuB,CAAC;YACvC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAEvB,OAAO,CAAC,IAAI,GAAG,wBAAwB,CAAC;YACxC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAEvB,OAAO,CAAC,IAAI,GAAG,oBAAoB,CAAC;YACpC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAEvB,OAAO,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC;YAE3D,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC;YAChD,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,OAAO,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC;YAC9E,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;YAClF,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;YAClF,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;QAE3F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAChC,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;QAC5C,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAC3E,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/E,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;QACvF,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,eAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,UAAkB,EAAE,OAAY;QACpD,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,8BAA8B,UAAU,EAAE,CAAC,CAAC,CAAC;YACpE,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAEvC,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACrD,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC,CAAC;gBAC5D,OAAO;YACT,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC,CAAC;YACxD,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACnD,MAAM,eAAe,GAAG,MAAM,aAAa,CAAC,aAAa,CAAC,UAAU,EAAE;gBACpE,KAAK,EAAE,cAAc;gBACrB,UAAU,EAAE,EAAE;gBACd,eAAe,EAAE,IAAI;gBACrB,aAAa,EAAE,IAAI;aACpB,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC,CAAC;YAC/D,MAAM,YAAY,GAAG,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE,CAAC,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;YAC1G,MAAM,cAAc,CAAC,oBAAoB,CAAC,UAAU,EAAE,eAAe,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YAE7F,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC,CAAC;YAC7D,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACvC,OAAO,CAAC,GAAG,CAAC,eAAe,UAAU,EAAE,CAAC,CAAC;YACzC,OAAO,CAAC,GAAG,CAAC,sBAAsB,eAAe,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YACpE,OAAO,CAAC,GAAG,CAAC,yBAAyB,eAAe,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;YACzE,OAAO,CAAC,GAAG,CAAC,kBAAkB,eAAe,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC;YAErE,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAC1C,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;YAC5D,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;YACrD,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QAEzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,+BAA+B,CAAC,EAAE,KAAK,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC/B,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;QAE7C,MAAM,QAAQ,GAAG;YACf,EAAE,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO,EAAE;YACtE,EAAE,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO,EAAE;YACtE,EAAE,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE;SACnE,CAAC;QAEF,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EACxB,eAAK,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,EAC3C,eAAK,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;CACF;AAnID,oCAmIC"}