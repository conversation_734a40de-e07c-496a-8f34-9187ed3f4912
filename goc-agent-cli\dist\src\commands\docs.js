"use strict";
/**
 * Documentation Command
 *
 * Intelligent documentation generation with code analysis and best practices
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocsCommand = void 0;
const chalk_1 = __importDefault(require("chalk"));
const inquirer_1 = __importDefault(require("inquirer"));
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
class DocsCommand {
    constructor(core) {
        this.core = core;
    }
    register(program) {
        const docsCmd = program
            .command('docs')
            .description('Intelligent documentation generation');
        docsCmd
            .command('generate [project-path]')
            .description('Generate comprehensive project documentation')
            .option('-f, --format <format>', 'Output format (markdown|html|pdf|json)', 'markdown')
            .option('-o, --output <path>', 'Output file path')
            .option('-l, --language <lang>', 'Primary programming language', 'javascript')
            .option('--api', 'Include API documentation')
            .option('--examples', 'Include code examples')
            .option('--architecture', 'Include architecture documentation')
            .option('--deployment', 'Include deployment documentation')
            .option('--testing', 'Include testing documentation')
            .option('--template <template>', 'Use custom template')
            .action(async (projectPath, options) => {
            await this.generateDocumentation(projectPath || process.cwd(), options);
        });
        docsCmd
            .command('api [project-path]')
            .description('Generate API documentation from code')
            .option('-l, --language <lang>', 'Programming language', 'javascript')
            .option('-f, --format <format>', 'Output format (markdown|html|json)', 'markdown')
            .option('-o, --output <path>', 'Output file path')
            .action(async (projectPath, options) => {
            await this.generateAPIDocumentation(projectPath || process.cwd(), options);
        });
        docsCmd
            .command('examples [project-path]')
            .description('Generate code examples')
            .option('-l, --language <lang>', 'Programming language', 'javascript')
            .option('-o, --output <path>', 'Output file path')
            .action(async (projectPath, options) => {
            await this.generateExamples(projectPath || process.cwd(), options);
        });
        docsCmd
            .command('readme [project-path]')
            .description('Generate or update README.md')
            .option('--update', 'Update existing README.md')
            .action(async (projectPath, options) => {
            await this.generateReadme(projectPath || process.cwd(), options);
        });
        docsCmd
            .command('interactive')
            .alias('i')
            .description('Interactive documentation generation')
            .action(async () => {
            await this.interactiveGeneration();
        });
    }
    async generateDocumentation(projectPath, options) {
        try {
            console.log(chalk_1.default.blue(`📚 Generating documentation for: ${projectPath}`));
            console.log(chalk_1.default.dim(`Format: ${options.format}, Language: ${options.language}`));
            console.log(chalk_1.default.dim('─'.repeat(60)));
            // Check if GOC Core has documentation generator
            // For now, we'll create a simplified implementation
            const docGenerator = this.createDocumentationGenerator();
            const docOptions = {
                format: options.format,
                includeExamples: options.examples,
                includeAPI: options.api,
                includeArchitecture: options.architecture,
                includeDeployment: options.deployment,
                includeTesting: options.testing,
                language: options.language,
                outputPath: options.output,
                template: options.template
            };
            console.log(chalk_1.default.blue('🔍 Analyzing project...'));
            const projectAnalysis = await this.analyzeProject(projectPath);
            console.log(chalk_1.default.blue('📝 Generating documentation sections...'));
            const documentation = await this.generateProjectDocs(projectAnalysis, docOptions);
            console.log(chalk_1.default.blue('📄 Documentation Generated:'));
            console.log(`Title: ${documentation.title}`);
            console.log(`Sections: ${documentation.sections.length}`);
            console.log(`Format: ${options.format}`);
            console.log();
            // Show sections
            documentation.sections.forEach((section, index) => {
                console.log(`${index + 1}. ${section.title} (${section.type})`);
            });
            console.log();
            // Export documentation
            const outputPath = options.output || `docs.${options.format}`;
            await this.exportDocumentation(documentation, options.format, outputPath);
            console.log(chalk_1.default.green(`✅ Documentation saved to: ${outputPath}`));
            // Show next steps
            console.log(chalk_1.default.blue('💡 Next Steps:'));
            console.log('• Review the generated documentation for accuracy');
            console.log('• Customize sections as needed');
            console.log('• Add project-specific details');
            console.log('• Consider setting up automated documentation updates');
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Documentation generation failed:'), error);
        }
    }
    async generateAPIDocumentation(projectPath, options) {
        try {
            console.log(chalk_1.default.blue(`📋 Generating API documentation for: ${projectPath}`));
            console.log(chalk_1.default.dim('─'.repeat(60)));
            console.log(chalk_1.default.blue('🔍 Analyzing code for API elements...'));
            const apiDocs = await this.extractAPIDocumentation(projectPath, options.language);
            console.log(chalk_1.default.blue('📊 API Analysis Results:'));
            console.log(`Functions: ${apiDocs.functions.length}`);
            console.log(`Classes: ${apiDocs.classes.length}`);
            console.log(`Interfaces: ${apiDocs.interfaces.length}`);
            console.log();
            if (apiDocs.functions.length > 0) {
                console.log(chalk_1.default.blue('🔧 Functions:'));
                apiDocs.functions.slice(0, 5).forEach((func, index) => {
                    console.log(`${index + 1}. ${func.name}(${func.parameters.map((p) => p.name).join(', ')})`);
                    console.log(chalk_1.default.dim(`   ${func.description}`));
                });
                if (apiDocs.functions.length > 5) {
                    console.log(chalk_1.default.dim(`   ... and ${apiDocs.functions.length - 5} more`));
                }
                console.log();
            }
            if (apiDocs.classes.length > 0) {
                console.log(chalk_1.default.blue('🏗️ Classes:'));
                apiDocs.classes.slice(0, 3).forEach((cls, index) => {
                    console.log(`${index + 1}. ${cls.name}`);
                    console.log(chalk_1.default.dim(`   ${cls.description}`));
                    console.log(chalk_1.default.dim(`   Methods: ${cls.methods.length}, Properties: ${cls.properties.length}`));
                });
                if (apiDocs.classes.length > 3) {
                    console.log(chalk_1.default.dim(`   ... and ${apiDocs.classes.length - 3} more`));
                }
                console.log();
            }
            // Export API documentation
            const content = this.formatAPIDocumentation(apiDocs, options.format);
            const outputPath = options.output || `api-docs.${options.format}`;
            fs_1.default.writeFileSync(outputPath, content, 'utf-8');
            console.log(chalk_1.default.green(`✅ API documentation saved to: ${outputPath}`));
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ API documentation generation failed:'), error);
        }
    }
    async generateExamples(projectPath, options) {
        try {
            console.log(chalk_1.default.blue(`💡 Generating code examples for: ${projectPath}`));
            console.log(chalk_1.default.dim('─'.repeat(60)));
            const examples = await this.createCodeExamples(projectPath, options.language);
            console.log(chalk_1.default.blue('📝 Generated Examples:'));
            examples.forEach((example, index) => {
                console.log(`${index + 1}. ${example.title}`);
                console.log(chalk_1.default.dim(`   ${example.description}`));
                console.log();
                console.log(`\`\`\`${example.language}`);
                console.log(example.code.substring(0, 200) + (example.code.length > 200 ? '...' : ''));
                console.log('```');
                console.log();
            });
            // Save examples
            const content = this.formatExamples(examples);
            const outputPath = options.output || 'examples.md';
            fs_1.default.writeFileSync(outputPath, content, 'utf-8');
            console.log(chalk_1.default.green(`✅ Examples saved to: ${outputPath}`));
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Examples generation failed:'), error);
        }
    }
    async generateReadme(projectPath, options) {
        try {
            console.log(chalk_1.default.blue(`📄 Generating README.md for: ${projectPath}`));
            console.log(chalk_1.default.dim('─'.repeat(60)));
            const readmePath = path_1.default.join(projectPath, 'README.md');
            const exists = fs_1.default.existsSync(readmePath);
            if (exists && !options.update) {
                const { overwrite } = await inquirer_1.default.prompt([{
                        type: 'confirm',
                        name: 'overwrite',
                        message: 'README.md already exists. Overwrite?',
                        default: false
                    }]);
                if (!overwrite) {
                    console.log(chalk_1.default.yellow('README generation cancelled.'));
                    return;
                }
            }
            console.log(chalk_1.default.blue('🔍 Analyzing project structure...'));
            const projectAnalysis = await this.analyzeProject(projectPath);
            console.log(chalk_1.default.blue('📝 Generating README content...'));
            const readmeContent = await this.generateReadmeContent(projectAnalysis);
            fs_1.default.writeFileSync(readmePath, readmeContent, 'utf-8');
            console.log(chalk_1.default.green(`✅ README.md ${exists ? 'updated' : 'created'} successfully`));
            console.log(chalk_1.default.blue('📋 README Sections:'));
            console.log('• Project title and description');
            console.log('• Installation instructions');
            console.log('• Usage examples');
            console.log('• API reference (if applicable)');
            console.log('• Contributing guidelines');
            console.log('• License information');
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ README generation failed:'), error);
        }
    }
    async interactiveGeneration() {
        try {
            console.log(chalk_1.default.blue('🎯 Interactive Documentation Generation'));
            console.log(chalk_1.default.dim('─'.repeat(60)));
            const answers = await inquirer_1.default.prompt([
                {
                    type: 'input',
                    name: 'projectPath',
                    message: 'Project path:',
                    default: process.cwd()
                },
                {
                    type: 'list',
                    name: 'format',
                    message: 'Output format:',
                    choices: ['markdown', 'html', 'pdf', 'json']
                },
                {
                    type: 'list',
                    name: 'language',
                    message: 'Primary programming language:',
                    choices: ['javascript', 'typescript', 'python', 'php', 'java', 'go', 'rust']
                },
                {
                    type: 'checkbox',
                    name: 'sections',
                    message: 'Include sections:',
                    choices: [
                        { name: 'API Documentation', value: 'api' },
                        { name: 'Code Examples', value: 'examples' },
                        { name: 'Architecture', value: 'architecture' },
                        { name: 'Deployment', value: 'deployment' },
                        { name: 'Testing', value: 'testing' }
                    ]
                },
                {
                    type: 'input',
                    name: 'output',
                    message: 'Output file path (optional):',
                    default: ''
                }
            ]);
            const options = {
                format: answers.format,
                language: answers.language,
                api: answers.sections.includes('api'),
                examples: answers.sections.includes('examples'),
                architecture: answers.sections.includes('architecture'),
                deployment: answers.sections.includes('deployment'),
                testing: answers.sections.includes('testing'),
                output: answers.output || undefined
            };
            await this.generateDocumentation(answers.projectPath, options);
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Interactive generation failed:'), error);
        }
    }
    // Helper methods
    createDocumentationGenerator() {
        // Simplified documentation generator
        return {
            generateProjectDocumentation: async (projectPath, options) => {
                // This would use the actual DocumentationGenerator from GOC Core
                return {
                    title: 'Project Documentation',
                    description: 'Generated project documentation',
                    version: '1.0.0',
                    sections: []
                };
            }
        };
    }
    async analyzeProject(projectPath) {
        const analysis = {
            name: path_1.default.basename(projectPath),
            description: '',
            version: '1.0.0',
            type: 'general',
            framework: 'unknown',
            dependencies: [],
            hasTests: false,
            hasDocker: false
        };
        try {
            // Check for package.json
            const packageJsonPath = path_1.default.join(projectPath, 'package.json');
            if (fs_1.default.existsSync(packageJsonPath)) {
                const packageJson = JSON.parse(fs_1.default.readFileSync(packageJsonPath, 'utf-8'));
                analysis.name = packageJson.name || analysis.name;
                analysis.description = packageJson.description || analysis.description;
                analysis.version = packageJson.version || analysis.version;
                analysis.type = 'nodejs';
                analysis.dependencies = Object.keys(packageJson.dependencies || {});
            }
            // Check for composer.json
            const composerJsonPath = path_1.default.join(projectPath, 'composer.json');
            if (fs_1.default.existsSync(composerJsonPath)) {
                const composerJson = JSON.parse(fs_1.default.readFileSync(composerJsonPath, 'utf-8'));
                analysis.name = composerJson.name || analysis.name;
                analysis.description = composerJson.description || analysis.description;
                analysis.type = 'php';
            }
            // Check for tests
            const testDirs = ['test', 'tests', '__tests__', 'spec'];
            analysis.hasTests = testDirs.some(dir => fs_1.default.existsSync(path_1.default.join(projectPath, dir)));
            // Check for Docker
            analysis.hasDocker = fs_1.default.existsSync(path_1.default.join(projectPath, 'Dockerfile'));
        }
        catch (error) {
            console.warn('Project analysis warning:', error);
        }
        return analysis;
    }
    async generateProjectDocs(analysis, options) {
        const sections = [];
        // Overview section
        sections.push({
            title: 'Overview',
            content: `# ${analysis.name}\n\n${analysis.description}\n\nVersion: ${analysis.version}`,
            level: 1,
            type: 'overview'
        });
        // Installation section
        sections.push({
            title: 'Installation',
            content: this.generateInstallationSection(analysis),
            level: 1,
            type: 'installation'
        });
        // Usage section
        sections.push({
            title: 'Usage',
            content: this.generateUsageSection(analysis),
            level: 1,
            type: 'usage'
        });
        return {
            title: analysis.name,
            description: analysis.description,
            version: analysis.version,
            sections
        };
    }
    async extractAPIDocumentation(projectPath, language) {
        // Simplified API extraction - in production, this would use the context engine
        return {
            functions: [
                {
                    name: 'exampleFunction',
                    description: 'An example function',
                    parameters: [
                        { name: 'param1', type: 'string', description: 'First parameter', required: true }
                    ],
                    returns: { type: 'boolean', description: 'Success status' },
                    examples: ['exampleFunction("test")']
                }
            ],
            classes: [
                {
                    name: 'ExampleClass',
                    description: 'An example class',
                    constructor: { parameters: [] },
                    methods: [
                        {
                            name: 'exampleMethod',
                            description: 'An example method',
                            parameters: [],
                            returns: { type: 'void', description: 'No return value' },
                            visibility: 'public'
                        }
                    ],
                    properties: []
                }
            ],
            interfaces: []
        };
    }
    async createCodeExamples(projectPath, language) {
        return [
            {
                title: 'Basic Usage',
                description: 'Basic example of how to use this project',
                code: this.generateBasicExample(language),
                language
            },
            {
                title: 'Advanced Usage',
                description: 'Advanced example with configuration',
                code: this.generateAdvancedExample(language),
                language
            }
        ];
    }
    generateInstallationSection(analysis) {
        let content = '# Installation\n\n';
        if (analysis.type === 'nodejs') {
            content += '```bash\nnpm install\n```\n\n';
        }
        else if (analysis.type === 'php') {
            content += '```bash\ncomposer install\n```\n\n';
        }
        else {
            content += 'Follow the installation instructions for your platform.\n\n';
        }
        return content;
    }
    generateUsageSection(analysis) {
        let content = '# Usage\n\n';
        content += 'Basic usage example:\n\n';
        content += `\`\`\`${analysis.type === 'nodejs' ? 'javascript' : analysis.type}\n`;
        content += this.generateBasicExample(analysis.type);
        content += '\n```\n\n';
        return content;
    }
    generateBasicExample(language) {
        const examples = {
            javascript: 'const app = require("./index");\napp.start();',
            php: '<?php\nrequire_once "vendor/autoload.php";\n$app = new App();\n$app->run();',
            python: 'from app import main\nmain()',
            general: '// Basic usage example'
        };
        return examples[language] || examples.general;
    }
    // Public method for CLI integration
    async generateDocs(path, options) {
        await this.generateDocumentation(path, options);
    }
    generateAdvancedExample(language) {
        const examples = {
            javascript: 'const app = require("./index");\nconst config = { port: 3000 };\napp.start(config);',
            php: '<?php\n$config = ["port" => 3000];\n$app = new App($config);\n$app->run();',
            python: 'from app import main\nconfig = {"port": 3000}\nmain(config)',
            general: '// Advanced usage example'
        };
        return examples[language] || examples.general;
    }
    formatAPIDocumentation(api, format) {
        if (format === 'json') {
            return JSON.stringify(api, null, 2);
        }
        let content = '# API Documentation\n\n';
        if (api.functions.length > 0) {
            content += '## Functions\n\n';
            api.functions.forEach((func) => {
                content += `### ${func.name}\n\n${func.description}\n\n`;
            });
        }
        if (api.classes.length > 0) {
            content += '## Classes\n\n';
            api.classes.forEach((cls) => {
                content += `### ${cls.name}\n\n${cls.description}\n\n`;
            });
        }
        return content;
    }
    formatExamples(examples) {
        let content = '# Code Examples\n\n';
        examples.forEach((example, index) => {
            content += `## ${example.title}\n\n`;
            content += `${example.description}\n\n`;
            content += `\`\`\`${example.language}\n${example.code}\n\`\`\`\n\n`;
        });
        return content;
    }
    async generateReadmeContent(analysis) {
        return `# ${analysis.name}

${analysis.description}

## Installation

${this.generateInstallationSection(analysis)}

## Usage

${this.generateUsageSection(analysis)}

## Contributing

Contributions are welcome! Please read the contributing guidelines before submitting a pull request.

## License

This project is licensed under the MIT License.
`;
    }
    async exportDocumentation(documentation, format, outputPath) {
        let content;
        switch (format) {
            case 'json':
                content = JSON.stringify(documentation, null, 2);
                break;
            case 'html':
                content = this.convertToHTML(documentation);
                break;
            default:
                content = this.convertToMarkdown(documentation);
        }
        fs_1.default.writeFileSync(outputPath, content, 'utf-8');
    }
    convertToMarkdown(documentation) {
        let content = `# ${documentation.title}\n\n`;
        content += `${documentation.description}\n\n`;
        documentation.sections.forEach((section) => {
            content += section.content + '\n\n';
        });
        return content;
    }
    convertToHTML(documentation) {
        const markdown = this.convertToMarkdown(documentation);
        return `<!DOCTYPE html>
<html>
<head>
    <title>${documentation.title}</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
        code { background: #f5f5f5; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
    <pre>${markdown}</pre>
</body>
</html>`;
    }
}
exports.DocsCommand = DocsCommand;
//# sourceMappingURL=docs.js.map