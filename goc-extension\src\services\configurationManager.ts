import * as vscode from 'vscode';
import fetch from 'node-fetch';
import { BackendAPIClient } from './backendApiClient';

export interface GocConfiguration {
    provider: string;
    model: string;
    apiKey: string;
    autoTraining: boolean;
    webResearch: boolean;
    contextLines: number;
    showProgress: boolean;
    autoSave: boolean;
    autoAnalysis: boolean;
}

export interface ProviderConfig {
    name: string;
    displayName: string;
    models: string[];
    requiresApiKey: boolean;
    defaultModel: string;
}

export class ConfigurationManager {
    private static readonly CONFIG_SECTION = 'gocAgent';
    private configuration: GocConfiguration;
    private backendClient: BackendAPIClient;
    private providers: ProviderConfig[] = [
        {
            name: 'ollama',
            displayName: 'Ollama (Local)',
            models: [], // Will be dynamically loaded
            requiresApiKey: false,
            defaultModel: 'llama3.2:3b'
        },
        {
            name: 'goc',
            displayName: 'GOC Agent Model',
            models: ['goc-agent-cloud', 'goc-agent-dev'],
            requiresApiKey: true,
            defaultModel: 'goc-agent-cloud'
        }
        // Other providers hidden for now
        // {
        //     name: 'openai',
        //     displayName: 'OpenAI',
        //     models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
        //     requiresApiKey: true,
        //     defaultModel: 'gpt-4'
        // },
        // {
        //     name: 'groq',
        //     displayName: 'Groq',
        //     models: ['llama-3.1-70b-versatile', 'llama-3.1-8b-instant'],
        //     requiresApiKey: true,
        //     defaultModel: 'llama-3.1-70b-versatile'
        // },
        // {
        //     name: 'gemini',
        //     displayName: 'Google Gemini',
        //     models: ['gemini-pro', 'gemini-1.5-pro'],
        //     requiresApiKey: true,
        //     defaultModel: 'gemini-pro'
        // },
        // {
        //     name: 'claude',
        //     displayName: 'Anthropic Claude',
        //     models: ['claude-3-sonnet-20240229', 'claude-3-haiku-20240307'],
        //     requiresApiKey: true,
        //     defaultModel: 'claude-3-sonnet-20240229'
        // }
    ];

    constructor(context: vscode.ExtensionContext) {
        this.configuration = this.loadConfiguration();
        this.backendClient = new BackendAPIClient(context);
    }

    getConfiguration(): GocConfiguration {
        return { ...this.configuration };
    }

    async getProviders(): Promise<ProviderConfig[]> {
        // Refresh Ollama models dynamically
        await this.refreshOllamaModels();
        return [...this.providers];
    }

    async refreshOllamaModels(): Promise<void> {
        try {
            const ollamaProvider = this.providers.find(p => p.name === 'ollama');
            if (!ollamaProvider) return;

            const models = await this.fetchOllamaModels();
            ollamaProvider.models = models;

            if (models.length === 0) {
                // Show helpful message if no models are installed
                vscode.window.showWarningMessage(
                    'No Ollama models found. Install models with: ollama pull llama3.2:3b',
                    'Install Models',
                    'Learn More'
                ).then(selection => {
                    if (selection === 'Install Models') {
                        vscode.env.openExternal(vscode.Uri.parse('https://ollama.ai/library'));
                    } else if (selection === 'Learn More') {
                        vscode.env.openExternal(vscode.Uri.parse('https://ollama.ai/download'));
                    }
                });
            }
        } catch (error) {
            console.warn('Failed to refresh Ollama models:', error);
        }
    }

    private async fetchOllamaModels(): Promise<string[]> {
        try {
            const baseUrl = 'http://localhost:11434';
            const response = await fetch(`${baseUrl}/api/tags`, {
                method: 'GET',
                signal: AbortSignal.timeout(5000)
            });

            if (response.ok) {
                const data = await response.json();
                const models = data.models?.map((model: any) => model.name) || [];

                // Sort models with recommended ones first
                const recommendedOrder = [
                    'llama3.2:3b',
                    'llama3.2:1b',
                    'codellama:7b',
                    'codellama:13b',
                    'mistral:7b',
                    'deepseek-coder:6.7b',
                    'qwen2.5-coder:7b'
                ];

                return models.sort((a: string, b: string) => {
                    const aIndex = recommendedOrder.indexOf(a);
                    const bIndex = recommendedOrder.indexOf(b);

                    if (aIndex !== -1 && bIndex !== -1) {
                        return aIndex - bIndex;
                    } else if (aIndex !== -1) {
                        return -1;
                    } else if (bIndex !== -1) {
                        return 1;
                    } else {
                        return a.localeCompare(b);
                    }
                });
            } else {
                return [];
            }
        } catch (error) {
            console.warn('Ollama not available:', error);
            return [];
        }
    }

    getProvider(name: string): ProviderConfig | undefined {
        return this.providers.find(p => p.name === name);
    }

    getCurrentProvider(): ProviderConfig | undefined {
        return this.getProvider(this.configuration.provider);
    }

    async setProvider(providerName: string): Promise<void> {
        const provider = this.getProvider(providerName);
        if (!provider) {
            throw new Error(`Unknown provider: ${providerName}`);
        }

        const config = vscode.workspace.getConfiguration(ConfigurationManager.CONFIG_SECTION);
        await config.update('provider', providerName, vscode.ConfigurationTarget.Global);
        
        // Set default model for the provider
        await config.update('model', provider.defaultModel, vscode.ConfigurationTarget.Global);
        
        this.reloadConfiguration();
    }

    async setModel(modelName: string): Promise<void> {
        const currentProvider = this.getCurrentProvider();
        if (!currentProvider) {
            throw new Error('No provider selected');
        }

        if (!currentProvider.models.includes(modelName)) {
            throw new Error(`Model ${modelName} not available for provider ${currentProvider.name}`);
        }

        const config = vscode.workspace.getConfiguration(ConfigurationManager.CONFIG_SECTION);
        await config.update('model', modelName, vscode.ConfigurationTarget.Global);
        
        this.reloadConfiguration();
    }

    async setApiKey(apiKey: string): Promise<void> {
        const config = vscode.workspace.getConfiguration(ConfigurationManager.CONFIG_SECTION);
        await config.update('apiKey', apiKey, vscode.ConfigurationTarget.Global);
        
        this.reloadConfiguration();
    }

    async updateSetting<K extends keyof GocConfiguration>(
        key: K,
        value: GocConfiguration[K]
    ): Promise<void> {
        const config = vscode.workspace.getConfiguration(ConfigurationManager.CONFIG_SECTION);
        await config.update(key, value, vscode.ConfigurationTarget.Global);
        
        this.reloadConfiguration();
    }

    reloadConfiguration(): void {
        this.configuration = this.loadConfiguration();
    }

    private loadConfiguration(): GocConfiguration {
        const config = vscode.workspace.getConfiguration(ConfigurationManager.CONFIG_SECTION);
        
        return {
            provider: config.get<string>('provider') || 'ollama',
            model: config.get<string>('model') || 'llama3.2:3b',
            apiKey: config.get<string>('apiKey') || '', // Not needed for Ollama
            autoTraining: config.get<boolean>('autoTraining') ?? true,
            webResearch: config.get<boolean>('webResearch') ?? true,
            contextLines: config.get<number>('contextLines') || 50,
            showProgress: config.get<boolean>('showProgress') ?? true,
            autoSave: config.get<boolean>('autoSave') ?? false,
            autoAnalysis: config.get<boolean>('autoAnalysis') ?? false
        };
    }

    validateConfiguration(): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];
        const config = this.getConfiguration();

        // Check if provider exists
        const provider = this.getProvider(config.provider);
        if (!provider) {
            errors.push(`Unknown provider: ${config.provider}`);
        } else {
            // Check if model is valid for provider
            if (!provider.models.includes(config.model)) {
                errors.push(`Model ${config.model} not available for provider ${provider.name}`);
            }

            // Check API key if required
            if (provider.requiresApiKey && !config.apiKey) {
                errors.push(`API key required for provider ${provider.name}`);
            }
        }

        // Validate numeric settings
        if (config.contextLines < 10 || config.contextLines > 200) {
            errors.push('Context lines must be between 10 and 200');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    async promptForApiKey(providerName?: string): Promise<string | undefined> {
        const provider = providerName ? this.getProvider(providerName) : this.getCurrentProvider();
        if (!provider) {
            return undefined;
        }

        const apiKey = await vscode.window.showInputBox({
            prompt: `Enter API key for ${provider.displayName}`,
            password: true,
            placeHolder: 'Your API key...',
            validateInput: (value) => {
                if (!value || value.trim().length === 0) {
                    return 'API key cannot be empty';
                }
                return null;
            }
        });

        if (apiKey) {
            await this.setApiKey(apiKey);
        }

        return apiKey;
    }

    async selectProvider(): Promise<string | undefined> {
        const items = this.providers.map(provider => {
            let description = '';
            let detail = '';

            if (provider.name === 'ollama') {
                description = 'Free local AI models';
                detail = 'No registration required - Install Ollama and pull models locally';
            } else if (provider.name === 'goc') {
                description = 'Hosted AI models';
                detail = 'Registration required - 50 free requests/month, $29/month for 500 requests';
            }

            return {
                label: provider.displayName,
                description,
                detail,
                value: provider.name
            };
        });

        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: 'Choose between local (Ollama) or hosted (GOC Agent) models',
            matchOnDescription: true,
            matchOnDetail: true
        });

        if (selected) {
            await this.setProvider(selected.value);

            // Handle authentication for GOC Agent
            if (selected.value === 'goc') {
                await this.handleGocAgentAuth();
            }
        }

        return selected?.value;
    }

    async selectModel(): Promise<string | undefined> {
        const currentProvider = this.getCurrentProvider();
        if (!currentProvider) {
            vscode.window.showErrorMessage('Please select a provider first');
            return undefined;
        }

        if (currentProvider.name === 'ollama') {
            return await this.selectOllamaModel();
        } else if (currentProvider.name === 'goc') {
            return await this.selectGocAgentModel();
        }

        return undefined;
    }

    private async selectOllamaModel(): Promise<string | undefined> {
        // Refresh models for Ollama
        await this.refreshOllamaModels();
        const currentProvider = this.getCurrentProvider()!;

        if (currentProvider.models.length === 0) {
            await this.showOllamaSetupDialog();
            return undefined;
        }

        const items = currentProvider.models.map(model => ({
            label: this.formatOllamaModelName(model),
            description: model === this.configuration.model ? '(current)' : this.getOllamaModelDescription(model),
            detail: `Local model - ${this.getOllamaModelSize(model)}`,
            value: model
        }));

        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: `Select Ollama model (${currentProvider.models.length} installed)`,
            matchOnDescription: true
        });

        if (selected) {
            await this.setModel(selected.value);
        }

        return selected?.value;
    }

    private async selectGocAgentModel(): Promise<string | undefined> {
        const items = [
            {
                label: 'GOC Agent Model (Free)',
                description: '50 requests/month forever',
                detail: 'Perfect for trying GOC Agent - No payment required',
                value: 'goc-agent-cloud'
            },
            {
                label: 'GOC Agent Model (Developer)',
                description: '$29/month for 500 requests',
                detail: 'Full-featured model for professional development',
                value: 'goc-agent-dev'
            }
        ];

        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: 'Select GOC Agent model tier',
            matchOnDescription: true
        });

        if (selected) {
            await this.setModel(selected.value);

            // Show authentication info if not authenticated
            if (!this.configuration.apiKey) {
                await this.handleGocAgentAuth();
            }
        }

        return selected?.value;
    }

    getStatusText(): string {
        const provider = this.getCurrentProvider();
        if (!provider) {
            return 'No Provider';
        }

        if (provider.name === 'ollama') {
            return `Ollama: ${this.formatOllamaModelName(this.configuration.model)}`;
        } else if (provider.name === 'goc') {
            const modelName = this.configuration.model === 'goc-agent-cloud' ? 'Free' : 'Developer';
            return `GOC Agent: ${modelName}`;
        }

        return `${provider.displayName}: ${this.configuration.model}`;
    }

    private formatOllamaModelName(modelId: string): string {
        const nameMap: Record<string, string> = {
            'llama3.2:3b': 'Llama 3.2 3B',
            'llama3.2:1b': 'Llama 3.2 1B',
            'codellama:7b': 'Code Llama 7B',
            'codellama:13b': 'Code Llama 13B',
            'mistral:7b': 'Mistral 7B',
            'deepseek-coder:6.7b': 'DeepSeek Coder 6.7B',
            'qwen2.5-coder:7b': 'Qwen2.5 Coder 7B'
        };
        return nameMap[modelId] || modelId;
    }

    private getOllamaModelDescription(modelId: string): string {
        const descMap: Record<string, string> = {
            'llama3.2:3b': 'Recommended for most tasks',
            'llama3.2:1b': 'Lightweight and fast',
            'codellama:7b': 'Specialized for coding',
            'codellama:13b': 'Advanced coding model',
            'mistral:7b': 'General purpose',
            'deepseek-coder:6.7b': 'Optimized for code',
            'qwen2.5-coder:7b': 'Latest coding model'
        };
        return descMap[modelId] || 'Local AI model';
    }

    private getOllamaModelSize(modelId: string): string {
        const sizeMap: Record<string, string> = {
            'llama3.2:3b': '2.0GB',
            'llama3.2:1b': '1.3GB',
            'codellama:7b': '3.8GB',
            'codellama:13b': '7.3GB',
            'mistral:7b': '4.1GB',
            'deepseek-coder:6.7b': '3.8GB',
            'qwen2.5-coder:7b': '4.2GB'
        };
        return sizeMap[modelId] || 'Unknown size';
    }

    private async handleGocAgentAuth(): Promise<void> {
        const isAuthenticated = this.backendClient.isAuthenticated();

        if (isAuthenticated) {
            // Check subscription status
            const access = await this.backendClient.validateGocAgentAccess();
            if (access.hasAccess) {
                vscode.window.showInformationMessage(
                    `✅ Authenticated! Plan: ${access.plan} | Requests remaining: ${access.requestsRemaining}`
                );
                return;
            } else {
                vscode.window.showWarningMessage(
                    `⚠️ ${access.message} | Plan: ${access.plan}`,
                    'Upgrade Plan',
                    'View Usage'
                ).then(selection => {
                    if (selection === 'Upgrade Plan') {
                        vscode.env.openExternal(vscode.Uri.parse('https://goc-agent.com/pricing'));
                    } else if (selection === 'View Usage') {
                        this.showUserProfile();
                    }
                });
                return;
            }
        }

        const action = await vscode.window.showInformationMessage(
            'GOC Agent models require authentication. Choose an option:',
            'Register New Account',
            'Login Existing Account',
            'Learn More',
            'Cancel'
        );

        if (action === 'Register New Account') {
            await this.showRegistrationDialog();
        } else if (action === 'Login Existing Account') {
            await this.showLoginDialog();
        } else if (action === 'Learn More') {
            vscode.env.openExternal(vscode.Uri.parse('https://goc-agent.com/pricing'));
        }
    }

    public async showRegistrationDialog(): Promise<void> {
        const name = await vscode.window.showInputBox({
            prompt: 'Enter your full name',
            placeHolder: 'John Doe',
            validateInput: (value) => {
                if (!value || value.trim().length < 2) {
                    return 'Name must be at least 2 characters';
                }
                return null;
            }
        });

        if (!name) return;

        const email = await vscode.window.showInputBox({
            prompt: 'Enter your email address',
            placeHolder: '<EMAIL>',
            validateInput: (value) => {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!value || !emailRegex.test(value)) {
                    return 'Please enter a valid email address';
                }
                return null;
            }
        });

        if (!email) return;

        const password = await vscode.window.showInputBox({
            prompt: 'Create a password (minimum 6 characters)',
            password: true,
            validateInput: (value) => {
                if (!value || value.length < 6) {
                    return 'Password must be at least 6 characters';
                }
                return null;
            }
        });

        if (!password) return;

        vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: 'Creating account...',
            cancellable: false
        }, async () => {
            const result = await this.backendClient.register(name, email, password);

            if (result.success) {
                vscode.window.showInformationMessage(
                    `✅ Welcome to GOC Agent, ${result.user?.name}! You now have 50 free requests per month.`,
                    'View Profile'
                ).then(selection => {
                    if (selection === 'View Profile') {
                        this.showUserProfile();
                    }
                });
            } else {
                vscode.window.showErrorMessage(`❌ Registration failed: ${result.message}`);
            }
        });
    }

    public async showLoginDialog(): Promise<void> {
        const email = await vscode.window.showInputBox({
            prompt: 'Enter your email address',
            placeHolder: '<EMAIL>',
            validateInput: (value) => {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!value || !emailRegex.test(value)) {
                    return 'Please enter a valid email address';
                }
                return null;
            }
        });

        if (!email) return;

        const password = await vscode.window.showInputBox({
            prompt: 'Enter your password',
            password: true,
            validateInput: (value) => {
                if (!value || value.length === 0) {
                    return 'Password cannot be empty';
                }
                return null;
            }
        });

        if (!password) return;

        vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: 'Signing in...',
            cancellable: false
        }, async () => {
            const result = await this.backendClient.login(email, password);

            if (result.success) {
                vscode.window.showInformationMessage(
                    `✅ Welcome back, ${result.user?.name}!`,
                    'View Profile'
                ).then(selection => {
                    if (selection === 'View Profile') {
                        this.showUserProfile();
                    }
                });
            } else {
                vscode.window.showErrorMessage(`❌ Login failed: ${result.message}`);
            }
        });
    }

    public async showUserProfile(): Promise<void> {
        try {
            const user = await this.backendClient.getCurrentUser();
            const subscription = user.subscription;

            let message = `👤 ${user.name} (${user.email})\n\n`;

            if (subscription) {
                message += `💳 Plan: ${subscription.plan}\n`;
                message += `📊 Status: ${subscription.status}\n`;
                message += `🔢 Requests Remaining: ${subscription.api_requests_remaining}/${subscription.api_requests_limit}\n`;

                if (subscription.expires_at) {
                    message += `📅 Expires: ${new Date(subscription.expires_at).toLocaleDateString()}\n`;
                }
            } else {
                message += '⚠️ No subscription found';
            }

            const action = await vscode.window.showInformationMessage(
                message,
                'Upgrade Plan',
                'View Usage',
                'Logout'
            );

            if (action === 'Upgrade Plan') {
                vscode.env.openExternal(vscode.Uri.parse('https://goc-agent.com/pricing'));
            } else if (action === 'View Usage') {
                vscode.env.openExternal(vscode.Uri.parse('https://goc-agent.com/dashboard'));
            } else if (action === 'Logout') {
                await this.backendClient.logout();
                vscode.window.showInformationMessage('✅ Logged out successfully');
            }
        } catch (error: any) {
            vscode.window.showErrorMessage(`❌ Failed to load profile: ${error.message}`);
        }
    }



    public async logout(): Promise<void> {
        await this.backendClient.logout();
        vscode.window.showInformationMessage('✅ Logged out successfully');
    }

    public async getModelsByProvider(provider: string): Promise<string[]> {
        const providerConfig = this.getProvider(provider);
        if (!providerConfig) return [];

        if (provider === 'ollama') {
            await this.refreshOllamaModels();
        }

        return providerConfig.models;
    }

    public async checkOllamaStatus(): Promise<{
        isRunning: boolean;
        hasModels: boolean;
        models: string[];
    }> {
        try {
            const response = await fetch('http://localhost:11434/api/tags', {
                method: 'GET',
                signal: AbortSignal.timeout(5000)
            });

            if (response.ok) {
                const data = await response.json();
                const models = data.models?.map((model: any) => model.name) || [];

                return {
                    isRunning: true,
                    hasModels: models.length > 0,
                    models
                };
            } else {
                return {
                    isRunning: false,
                    hasModels: false,
                    models: []
                };
            }
        } catch (error) {
            return {
                isRunning: false,
                hasModels: false,
                models: []
            };
        }
    }

    public async showOllamaSetupDialog(): Promise<void> {
        const action = await vscode.window.showInformationMessage(
            'Ollama Setup Required',
            'Ollama is not installed or not running. Would you like to install it?',
            'Download Ollama',
            'View Models',
            'Cancel'
        );

        if (action === 'Download Ollama') {
            vscode.env.openExternal(vscode.Uri.parse('https://ollama.ai/download'));
        } else if (action === 'View Models') {
            vscode.env.openExternal(vscode.Uri.parse('https://ollama.ai/library'));
        }
    }

    /**
     * Get detailed status information for display
     */
    getDetailedStatus(): {
        provider: string;
        model: string;
        status: string;
        description: string;
        requiresAuth: boolean;
        isAuthenticated: boolean;
    } {
        const provider = this.getCurrentProvider();

        if (!provider) {
            return {
                provider: 'None',
                model: 'None',
                status: 'Not configured',
                description: 'No AI provider selected',
                requiresAuth: false,
                isAuthenticated: false
            };
        }

        if (provider.name === 'ollama') {
            return {
                provider: provider.displayName,
                model: this.formatOllamaModelName(this.configuration.model),
                status: 'Local',
                description: 'Free local AI models - No registration required',
                requiresAuth: false,
                isAuthenticated: true // Always "authenticated" for local models
            };
        } else if (provider.name === 'goc') {
            const isAuth = this.backendClient.isAuthenticated();
            const tierName = this.configuration.model === 'goc-agent-cloud' ? 'Free Tier' : 'Developer Tier';

            return {
                provider: provider.displayName,
                model: `${tierName}`,
                status: isAuth ? 'Authenticated' : 'Not authenticated',
                description: isAuth
                    ? `${tierName} - Hosted AI models`
                    : 'Registration required - 50 free requests/month, $29/month for 500 requests',
                requiresAuth: true,
                isAuthenticated: isAuth
            };
        }

        const isAuthenticated = provider.requiresApiKey ? !!this.configuration.apiKey : true;

        let status = 'Ready';
        let description = '';

        if (provider.name === 'ollama') {
            status = 'Local';
            description = 'Running locally on your machine';
        } else if (provider.name === 'goc') {
            if (!isAuthenticated) {
                status = 'Authentication required';
                description = 'Register at goc-agent.com to get API key';
            } else {
                status = 'Cloud';
                const tierName = this.configuration.model === 'goc-agent-cloud' ? 'Free' : 'Developer';
                description = `GOC Agent Model (${tierName} tier)`;
            }
        }

        return {
            provider: provider.displayName,
            model: this.formatModelName(this.configuration.model),
            status,
            description,
            requiresAuth: provider.requiresApiKey,
            isAuthenticated
        };
    }

    private formatModelName(modelId: string): string {
        if (!modelId) return 'None';

        // Handle Ollama models
        if (modelId.includes(':')) {
            return this.formatOllamaModelName(modelId);
        }

        // Handle GOC Agent models
        if (modelId === 'goc-agent-cloud') return 'Free Tier';
        if (modelId === 'goc-agent-dev') return 'Developer Tier';

        return modelId;
    }


}
