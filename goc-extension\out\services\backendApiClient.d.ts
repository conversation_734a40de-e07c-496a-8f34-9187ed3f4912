/**
 * Backend API Client for VS Code Extension
 *
 * Handles authentication and API requests to GOC Agent backend
 */
import * as vscode from 'vscode';
export interface User {
    id: string;
    name: string;
    email: string;
    subscription?: {
        plan: string;
        status: string;
        api_requests_remaining: number;
        api_requests_limit: number;
        expires_at?: string;
    };
}
export interface AuthResponse {
    success: boolean;
    token?: string;
    user?: User;
    message?: string;
}
export interface ChatRequest {
    messages: Array<{
        role: string;
        content: string;
    }>;
    model?: string;
    provider?: string;
    session_id?: string;
    max_tokens?: number;
    temperature?: number;
}
export interface ChatResponse {
    content: string;
    model: string;
    provider: string;
    usage?: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
    };
    session_id?: string;
}
export declare class BackendAPIClient {
    private baseUrl;
    private token;
    private context;
    constructor(context: vscode.ExtensionContext);
    private makeRequest;
    isAuthenticated(): boolean;
    register(name: string, email: string, password: string): Promise<AuthResponse>;
    login(email: string, password: string): Promise<AuthResponse>;
    logout(): Promise<void>;
    getCurrentUser(): Promise<User>;
    chat(request: ChatRequest): Promise<ChatResponse>;
    executeTask(task: string, sessionId?: string, provider?: string, model?: string, autoMode?: boolean): Promise<any>;
    getProviders(): Promise<any>;
    getStatus(): Promise<any>;
    healthCheck(): Promise<boolean>;
    private saveToken;
    private clearToken;
    /**
     * Check if user has access to GOC Agent models
     */
    validateGocAgentAccess(): Promise<{
        hasAccess: boolean;
        plan: string;
        requestsRemaining: number;
        message?: string;
    }>;
}
//# sourceMappingURL=backendApiClient.d.ts.map