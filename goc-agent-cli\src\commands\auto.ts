/**
 * Auto Mode Command
 * 
 * Intelligent autonomous task execution and decision making
 */

import chalk from 'chalk';
import inquirer from 'inquirer';
import { Command } from 'commander';
import { GocCore } from '@goc-agent/core';

export class AutoCommand {
  constructor(private core: GocCore) {}

  register(program: Command): void {
    const autoCmd = program
      .command('auto')
      .description('Intelligent autonomous task execution');

    autoCmd
      .command('execute <goal>')
      .description('Execute a goal autonomously')
      .option('--safe', 'Enable safe mode (default)', true)
      .option('--no-web', 'Disable web research')
      .option('--no-learning', 'Disable learning')
      .option('--timeout <minutes>', 'Timeout in minutes', '30')
      .option('--max-tasks <number>', 'Maximum concurrent tasks', '3')
      .action(async (goal, options) => {
        await this.executeGoal(goal, options);
      });

    autoCmd
      .command('status')
      .description('Show auto mode status')
      .action(async () => {
        await this.showStatus();
      });

    autoCmd
      .command('stop')
      .description('Stop all auto mode execution')
      .action(async () => {
        await this.stopExecution();
      });

    autoCmd
      .command('plan <goal>')
      .description('Create an execution plan without executing')
      .action(async (goal) => {
        await this.createPlan(goal);
      });

    autoCmd
      .command('decide')
      .description('Make an intelligent decision based on context')
      .option('--goal <goal>', 'Goal to achieve')
      .option('--context <context>', 'Current context (JSON)')
      .action(async (options) => {
        await this.makeDecision(options);
      });
  }

  async executeGoal(goal: string, options: any): Promise<void> {
    try {
      console.log(chalk.blue(`🤖 Starting autonomous execution: "${goal}"`));
      console.log(chalk.dim('─'.repeat(60)));

      const autoEngine = this.core.getAutoModeEngine();
      
      // Show configuration
      console.log(chalk.blue('⚙️ Configuration:'));
      console.log(`Safe Mode: ${options.safe ? chalk.green('Enabled') : chalk.red('Disabled')}`);
      console.log(`Web Research: ${options.web !== false ? chalk.green('Enabled') : chalk.red('Disabled')}`);
      console.log(`Learning: ${options.learning !== false ? chalk.green('Enabled') : chalk.red('Disabled')}`);
      console.log(`Timeout: ${options.timeout} minutes`);
      console.log(`Max Concurrent Tasks: ${options.maxTasks}`);
      console.log();

      // Confirm execution in non-safe mode
      if (!options.safe) {
        const { confirm } = await inquirer.prompt([{
          type: 'confirm',
          name: 'confirm',
          message: 'Safe mode is disabled. This may perform file operations. Continue?',
          default: false
        }]);

        if (!confirm) {
          console.log(chalk.yellow('Execution cancelled.'));
          return;
        }
      }

      console.log(chalk.blue('🚀 Executing goal...'));
      
      const startTime = Date.now();
      const plan = await autoEngine.executeGoal(goal, {
        projectPath: process.cwd(),
        safeMode: options.safe,
        enableWebResearch: options.web !== false,
        enableLearning: options.learning !== false,
        timeoutMinutes: parseInt(options.timeout),
        maxConcurrentTasks: parseInt(options.maxTasks)
      });

      const duration = Date.now() - startTime;

      console.log(chalk.green('✅ Execution completed!'));
      console.log();

      // Show plan results
      console.log(chalk.blue('📋 Execution Summary:'));
      console.log(`Plan ID: ${plan.id}`);
      console.log(`Status: ${this.getStatusColor(plan.status)}${plan.status}${chalk.reset()}`);
      console.log(`Progress: ${plan.progress.toFixed(1)}%`);
      console.log(`Duration: ${(duration / 1000).toFixed(1)}s`);
      console.log(`Tasks: ${plan.tasks.length}`);
      console.log();

      // Show task results
      console.log(chalk.blue('📝 Task Results:'));
      plan.tasks.forEach((task: any, index: number) => {
        const statusIcon = task.status === 'completed' ? '✅' : 
                          task.status === 'failed' ? '❌' : 
                          task.status === 'running' ? '🔄' : '⏳';
        
        console.log(`${index + 1}. ${statusIcon} ${task.description}`);
        console.log(chalk.dim(`   Type: ${task.type}, Priority: ${task.priority}`));
        
        if (task.actualDuration) {
          console.log(chalk.dim(`   Duration: ${task.actualDuration.toFixed(1)}s`));
        }
        
        if (task.error) {
          console.log(chalk.red(`   Error: ${task.error}`));
        }
        
        if (task.output && typeof task.output === 'object') {
          const outputPreview = JSON.stringify(task.output).substring(0, 100);
          console.log(chalk.dim(`   Output: ${outputPreview}${JSON.stringify(task.output).length > 100 ? '...' : ''}`));
        }
        
        console.log();
      });

      // Show recommendations
      const completedTasks = plan.tasks.filter((t: any) => t.status === 'completed');
      if (completedTasks.length > 0) {
        console.log(chalk.blue('💡 Recommendations:'));
        console.log('• Review the generated outputs for accuracy');
        console.log('• Consider running tests if code was generated');
        console.log('• Check for any manual steps that may be required');
        
        if (plan.tasks.some((t: any) => t.type === 'web_research')) {
          console.log('• Verify web research results are current and accurate');
        }
      }

    } catch (error) {
      console.error(chalk.red('❌ Auto execution failed:'), error);
    }
  }

  private async showStatus(): Promise<void> {
    try {
      console.log(chalk.blue('🤖 Auto Mode Status'));
      console.log(chalk.dim('─'.repeat(60)));

      const autoEngine = this.core.getAutoModeEngine();
      const status = autoEngine.getStatus();

      console.log(chalk.blue('📊 Current Status:'));
      console.log(`Active Plans: ${status.activePlans}`);
      console.log(`Running Tasks: ${status.runningTasks}`);
      console.log(`Total Tasks Completed: ${status.totalTasksCompleted}`);
      console.log(`Average Task Duration: ${status.averageTaskDuration.toFixed(1)}s`);
      console.log();

      if (status.activePlans === 0 && status.runningTasks === 0) {
        console.log(chalk.green('✅ No active auto mode execution'));
        console.log(chalk.dim('Use "goc auto execute <goal>" to start autonomous execution'));
      } else {
        console.log(chalk.yellow('⚠️ Auto mode is currently active'));
        console.log(chalk.dim('Use "goc auto stop" to stop all execution'));
      }

    } catch (error) {
      console.error(chalk.red('❌ Failed to get auto mode status:'), error);
    }
  }

  private async stopExecution(): Promise<void> {
    try {
      console.log(chalk.yellow('🛑 Stopping all auto mode execution...'));

      const autoEngine = this.core.getAutoModeEngine();
      await autoEngine.stopAll();

      console.log(chalk.green('✅ All auto mode execution stopped'));
    } catch (error) {
      console.error(chalk.red('❌ Failed to stop auto mode execution:'), error);
    }
  }

  private async createPlan(goal: string): Promise<void> {
    try {
      console.log(chalk.blue(`📋 Creating execution plan for: "${goal}"`));
      console.log(chalk.dim('─'.repeat(60)));

      const autoEngine = this.core.getAutoModeEngine();
      const plan = await autoEngine.createPlan(goal, {
        projectPath: process.cwd()
      });

      console.log(chalk.blue('📝 Execution Plan:'));
      console.log(`Plan ID: ${plan.id}`);
      console.log(`Goal: ${plan.goal}`);
      console.log(`Tasks: ${plan.tasks.length}`);
      console.log(`Estimated Duration: ${Math.ceil((plan.estimatedCompletion.getTime() - plan.createdAt.getTime()) / 1000)}s`);
      console.log();

      console.log(chalk.blue('📋 Task Breakdown:'));
      plan.tasks.forEach((task: any, index: number) => {
        console.log(`${index + 1}. ${task.description}`);
        console.log(chalk.dim(`   Type: ${task.type}`));
        console.log(chalk.dim(`   Priority: ${task.priority}`));
        console.log(chalk.dim(`   Estimated Duration: ${task.estimatedDuration}s`));
        
        if (task.dependencies.length > 0) {
          console.log(chalk.dim(`   Dependencies: ${task.dependencies.length} task(s)`));
        }
        
        console.log();
      });

      console.log(chalk.blue('💡 Next Steps:'));
      console.log('• Review the plan to ensure it meets your requirements');
      console.log('• Use "goc auto execute" to run the plan');
      console.log('• Modify the goal description if needed for better results');

    } catch (error) {
      console.error(chalk.red('❌ Failed to create execution plan:'), error);
    }
  }

  private async makeDecision(options: any): Promise<void> {
    try {
      console.log(chalk.blue('🧠 Making intelligent decision...'));
      console.log(chalk.dim('─'.repeat(60)));

      if (!options.goal) {
        const { goal } = await inquirer.prompt([{
          type: 'input',
          name: 'goal',
          message: 'What goal do you want to achieve?',
          validate: (input) => input.trim().length > 0 || 'Goal cannot be empty'
        }]);
        options.goal = goal;
      }

      let context = {};
      if (options.context) {
        try {
          context = JSON.parse(options.context);
        } catch {
          console.log(chalk.yellow('⚠️ Invalid context JSON, using empty context'));
        }
      }

      const autoEngine = this.core.getAutoModeEngine();
      const decision = await autoEngine.makeDecision({
        goal: options.goal,
        availableTools: ['context_search', 'web_research', 'code_analysis', 'learning'],
        currentContext: context,
        previousActions: [],
        constraints: ['no_files'] // Safe mode constraints
      });

      console.log(chalk.blue('🎯 Decision Result:'));
      console.log(`Recommended Action: ${chalk.bold(decision.action)}`);
      console.log(`Confidence: ${(decision.confidence * 100).toFixed(0)}%`);
      console.log(`Reasoning: ${decision.reasoning}`);
      console.log();

      if (decision.alternatives.length > 0) {
        console.log(chalk.blue('🔄 Alternative Actions:'));
        decision.alternatives.forEach((alt: any, index: number) => {
          console.log(`${index + 1}. ${alt}`);
        });
        console.log();
      }

      console.log(chalk.blue('💡 Next Steps:'));
      console.log(`• Use "goc auto execute '${options.goal}'" to execute the goal`);
      console.log('• Consider the alternative actions if the recommended action is not suitable');
      console.log('• Provide more context for better decision making');

    } catch (error) {
      console.error(chalk.red('❌ Failed to make decision:'), error);
    }
  }

  private getStatusColor(status: string): (text: string) => string {
    switch (status) {
      case 'completed':
        return chalk.green.bold;
      case 'failed':
        return chalk.red.bold;
      case 'executing':
        return chalk.yellow.bold;
      case 'planning':
        return chalk.blue.bold;
      default:
        return chalk.dim;
    }
  }
}
